# API接口测试数据补充完善文档

## 📋 **文档概述**
基于 apitest-url.mdc 中的接口顺序，补全每个API接口的请求数据和业务状态码响应格式化数据示例代码。

---

## 🚀 **第一阶段：用户注册登录** (10个接口)

### 📋 **测试目标**
建立最基础的用户认证体系，确保后续所有接口都有认证基础。

### 🔐 **核心认证流程**

#### 步骤1: 5.1 生成验证码 GET /api/captcha/generate

**请求参数示例：**
```json
{
    "type": "register",
    "phone": "13800138000"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "验证码生成成功",
    "data": {
        "captcha_id": "captcha_abc123_def456",
        "captcha_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expire_time": 300,
        "created_at": "2024-01-01 12:00:00"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "type": ["验证码类型不能为空"],
            "phone": ["手机号格式不正确"]
        }
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1052 - 验证码发送频率限制)：**
```json
{
    "code": 1052,
    "message": "验证码发送频率限制",
    "data": {
        "retry_after": 60,
        "remaining_attempts": 2,
        "reset_time": "2024-01-01 12:01:00"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 5.2 验证验证码 POST /api/captcha/verify

**请求参数示例：**
```json
{
    "captcha_id": "captcha_abc123_def456",
    "code": "1234"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "验证码验证成功",
    "data": {
        "verified": true,
        "captcha_id": "captcha_abc123_def456",
        "verified_at": "2024-01-01 12:00:30"
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "captcha_id": ["验证码ID不能为空"],
            "code": ["验证码不能为空"]
        }
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1050 - 验证码错误)：**
```json
{
    "code": 1050,
    "message": "验证码错误",
    "data": {
        "remaining_attempts": 2,
        "captcha_id": "captcha_abc123_def456",
        "error_count": 1
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1051 - 验证码已过期)：**
```json
{
    "code": 1051,
    "message": "验证码已过期",
    "data": {
        "captcha_id": "captcha_abc123_def456",
        "expired_at": "2024-01-01 11:55:00",
        "need_refresh": true
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 5.3 刷新验证码 POST /api/captcha/refresh

**请求参数示例：**
```json
{
    "captcha_id": "captcha_abc123_def456"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "验证码刷新成功",
    "data": {
        "captcha_id": "captcha_new123_def456",
        "captcha_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expire_time": 300,
        "created_at": "2024-01-01 12:01:00"
    },
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 验证码不存在)：**
```json
{
    "code": 404,
    "message": "验证码不存在",
    "data": {
        "captcha_id": "captcha_abc123_def456",
        "error_type": "not_found"
    },
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "captcha_id": ["验证码ID不能为空"]
        }
    },
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 4.1 用户注册 POST /api/register

**请求参数示例：**
```json
{
    "username": "testuser",
    "password": "password123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "verification_code": "1234",
    "invite_code": "INV123456",
    "agree_terms": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "status": "active",
        "created_at": "2024-01-01 12:02:00",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_expires_at": "2024-02-01 12:02:00"
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "username": ["用户名已存在"],
            "password": ["密码长度至少6位"],
            "email": ["邮箱格式不正确"],
            "phone": ["手机号格式不正确"],
            "verification_code": ["验证码不能为空"],
            "agree_terms": ["必须同意服务条款"]
        }
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1002 - 用户已存在)：**
```json
{
    "code": 1002,
    "message": "用户已存在",
    "data": {
        "username": "testuser",
        "conflict_field": "username",
        "existing_user_id": 12344
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1009 - 邮箱已存在)：**
```json
{
    "code": 1009,
    "message": "邮箱已存在",
    "data": {
        "email": "<EMAIL>",
        "conflict_field": "email",
        "existing_user_id": 12344
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 4.2 用户登录 POST /api/login

**请求参数示例：**
```json
{
    "login": "testuser",
    "password": "password123",
    "device_info": {
        "device_type": "web",
        "device_id": "device_abc123",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "ip_address": "*************"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "https://example.com/avatar/12345.jpg",
        "status": "active",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "refresh_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_expires_at": "2024-02-01 12:03:00",
        "last_login_at": "2024-01-01 12:03:00",
        "permissions": ["user.basic", "user.create", "user.read"]
    },
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1001 - 用户名或密码错误)：**
```json
{
    "code": 1001,
    "message": "用户名或密码错误",
    "data": {
        "login_attempts": 3,
        "remaining_attempts": 2,
        "lockout_time": null
    },
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1003 - 账户被锁定)：**
```json
{
    "code": 1003,
    "message": "账户被锁定",
    "data": {
        "lockout_reason": "too_many_failed_attempts",
        "locked_until": "2024-01-01 13:03:00",
        "remaining_time": 3600,
        "contact_support": true
    },
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 4.7 验证Token GET /api/verify

**请求参数示例：**
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "Token验证成功",
    "data": {
        "valid": true,
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "token_expires_at": "2024-02-01 12:03:00",
        "remaining_time": 2592000,
        "permissions": ["user.basic", "user.create", "user.read"]
    },
    "timestamp": 1640995440,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - Token无效或过期)：**
```json
{
    "code": 401,
    "message": "Token无效或过期",
    "data": {
        "valid": false,
        "error_type": "token_expired",
        "expired_at": "2024-01-01 11:03:00",
        "need_refresh": true
    },
    "timestamp": 1640995440,
    "request_id": "req_abc123_def456"
}
```

#### 步骤7: 4.4 刷新Token POST /api/refresh

**请求参数示例：**
```json
{
    "refresh_token": "refresh_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9_new...",
        "refresh_token": "refresh_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9_new...",
        "token_expires_at": "2024-02-01 12:04:00",
        "refreshed_at": "2024-01-01 12:04:00"
    },
    "timestamp": 1640995500,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - refresh_token无效或过期)：**
```json
{
    "code": 401,
    "message": "refresh_token无效或过期",
    "data": {
        "error_type": "refresh_token_expired",
        "need_relogin": true,
        "expired_at": "2024-01-01 11:04:00"
    },
    "timestamp": 1640995500,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "refresh_token": ["刷新令牌不能为空"]
        }
    },
    "timestamp": 1640995500,
    "request_id": "req_abc123_def456"
}
```

#### 步骤8: 4.3 用户登出 POST /api/logout

**请求参数示例：**
```json
{
    "all_devices": false
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "登出成功",
    "data": {
        "user_id": 12345,
        "logout_type": "single_device",
        "logged_out_at": "2024-01-01 12:05:00",
        "token_invalidated": true
    },
    "timestamp": 1640995560,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995560,
    "request_id": "req_abc123_def456"
}
```

#### 步骤9: 4.5 忘记密码 POST /api/forgot-password

**请求参数示例：**
```json
{
    "email": "<EMAIL>"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "密码重置邮件发送成功",
    "data": {
        "email": "<EMAIL>",
        "reset_token_expires_at": "2024-01-01 13:06:00",
        "sent_at": "2024-01-01 12:06:00",
        "email_masked": "te**@example.com"
    },
    "timestamp": 1640995620,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 用户不存在)：**
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": {
        "email": "<EMAIL>",
        "error_type": "user_not_found"
    },
    "timestamp": 1640995620,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "email": ["邮箱格式不正确"]
        }
    },
    "timestamp": 1640995620,
    "request_id": "req_abc123_def456"
}
```

#### 步骤10: 4.6 重置密码 POST /api/reset-password

**请求参数示例：**
```json
{
    "token": "reset_token_abc123_def456",
    "new_password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "密码重置成功",
    "data": {
        "user_id": 12345,
        "email": "<EMAIL>",
        "reset_at": "2024-01-01 12:07:00",
        "password_updated": true,
        "auto_login": false
    },
    "timestamp": 1640995680,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "token": ["重置令牌不能为空"],
            "new_password": ["密码长度至少6位"],
            "password_confirmation": ["两次密码输入不一致"]
        }
    },
    "timestamp": 1640995680,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1004 - 重置token无效或过期)：**
```json
{
    "code": 1004,
    "message": "重置token无效或过期",
    "data": {
        "token": "reset_token_abc123_def456",
        "error_type": "token_expired",
        "expired_at": "2024-01-01 11:07:00",
        "need_request_new": true
    },
    "timestamp": 1640995680,
    "request_id": "req_abc123_def456"
}
```

---

## 🔐 **第二阶段：用户安全验证** (4个接口)

### 📋 **测试目标**
验证登录用户的基本信息获取和权限查询，确保用户可以正常使用系统。

### 👤 **用户基础信息验证**

#### 步骤1: 24.1 用户中心信息 GET /api/user/profile

**请求参数示例：**
```json
{}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "https://example.com/avatar/12345.jpg",
        "nickname": "测试用户",
        "gender": "male",
        "birthday": "1990-01-01",
        "location": "北京市",
        "bio": "这是一个测试用户",
        "status": "active",
        "level": 5,
        "points": 1500,
        "credits": 2000,
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 12:07:00",
        "last_login_at": "2024-01-01 12:03:00",
        "email_verified": true,
        "phone_verified": true
    },
    "timestamp": 1640995740,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995740,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 23.1 获取用户权限 GET /api/permissions/user

**请求参数示例：**
```json
{
    "user_id": 12345
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "roles": [
            {
                "role_id": 1,
                "role_name": "普通用户",
                "role_code": "user",
                "assigned_at": "2024-01-01 10:00:00"
            }
        ],
        "permissions": [
            {
                "permission_id": 1,
                "permission_name": "基础权限",
                "permission_code": "user.basic",
                "resource": "*",
                "actions": ["read"]
            },
            {
                "permission_id": 2,
                "permission_name": "创建权限",
                "permission_code": "user.create",
                "resource": "projects",
                "actions": ["create", "read", "update"]
            }
        ],
        "permission_summary": {
            "total_permissions": 2,
            "active_permissions": 2,
            "last_updated": "2024-01-01 10:00:00"
        }
    },
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 用户不存在)：**
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": {
        "user_id": 12345,
        "error_type": "user_not_found"
    },
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (403 - 无权限)：**
```json
{
    "code": 403,
    "message": "无权限访问",
    "data": {
        "required_permission": "admin.user.read",
        "current_permissions": ["user.basic", "user.create"]
    },
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 23.2 检查用户权限 POST /api/permissions/check

**请求参数示例：**
```json
{
    "permission": "user.create",
    "resource_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "权限检查完成",
    "data": {
        "user_id": 12345,
        "permission": "user.create",
        "resource_id": "project_123",
        "has_permission": true,
        "permission_source": "role",
        "role_name": "普通用户",
        "checked_at": "2024-01-01 12:08:00",
        "expires_at": null
    },
    "timestamp": 1640995860,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995860,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 24.4 获取用户偏好设置 GET /api/user/preferences

**请求参数示例：**
```json
{}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "preferences": {
            "language": "zh-CN",
            "timezone": "Asia/Shanghai",
            "theme": "light",
            "notifications": {
                "email_enabled": true,
                "push_enabled": true,
                "sms_enabled": false,
                "marketing_enabled": false
            },
            "ai_settings": {
                "preferred_platforms": ["LiblibAI", "KlingAI", "MiniMax"],
                "default_quality": "high",
                "auto_retry": true,
                "timeout_preference": "standard"
            },
            "privacy": {
                "profile_visibility": "public",
                "work_visibility": "friends",
                "activity_tracking": true
            },
            "interface": {
                "sidebar_collapsed": false,
                "grid_view": true,
                "items_per_page": 20
            }
        },
        "last_updated": "2024-01-01 12:00:00",
        "version": 1
    },
    "timestamp": 1640995920,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995920,
    "request_id": "req_abc123_def456"
}
```

---

## 🌐 **第三阶段：WebSocket安全验证** (2个接口)

### 📋 **测试目标**
验证WebSocket服务的基础状态和认证机制，确保Python工具可以正常连接。

### 🔌 **WebSocket基础验证**

#### 步骤1: 7.4 WebSocket服务状态 GET /api/websocket/status

**请求参数示例：**
```json
{}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "service_status": "running",
        "server_info": {
            "host": "0.0.0.0",
            "port": 8080,
            "protocol": "wss",
            "started_at": "2024-01-01 10:00:00",
            "uptime": 7200
        },
        "connection_stats": {
            "total_connections": 150,
            "active_connections": 120,
            "python_tool_connections": 45,
            "web_tool_connections": 0
        },
        "performance": {
            "cpu_usage": 15.5,
            "memory_usage": 256,
            "message_rate": 1200,
            "error_rate": 0.01
        },
        "version": "1.0.0",
        "last_check": "2024-01-01 12:09:00"
    },
    "timestamp": 1640995980,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995980,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1012 - 服务不可用)：**
```json
{
    "code": 1012,
    "message": "WebSocket服务不可用",
    "data": {
        "service_status": "stopped",
        "error_reason": "service_crashed",
        "last_error": "Connection refused",
        "restart_required": true,
        "estimated_recovery_time": 300
    },
    "timestamp": 1640995980,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 7.1 WebSocket连接认证 POST /api/websocket/auth

**请求参数示例：**
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "channel": "python_tool_channel",
    "client_info": {
        "client_type": "python_tool",
        "client_version": "1.0.0",
        "user_agent": "PythonTool/1.0.0",
        "api_key": "encrypted_api_key_abc123"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "认证成功",
    "data": {
        "auth_success": true,
        "user_id": 12345,
        "username": "testuser",
        "channel": "python_tool_channel",
        "connection_id": "conn_abc123_def456",
        "websocket_url": "wss://api.tiptop.cn:8080/websocket",
        "auth_token": "ws_auth_token_abc123_def456",
        "expires_at": "2024-01-01 13:10:00",
        "permissions": ["websocket.connect", "websocket.receive", "websocket.send"],
        "rate_limits": {
            "messages_per_minute": 1000,
            "max_connections": 5
        },
        "authenticated_at": "2024-01-01 12:10:00"
    },
    "timestamp": 1640996040,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 认证失败)：**
```json
{
    "code": 401,
    "message": "WebSocket认证失败",
    "data": {
        "auth_success": false,
        "error_type": "invalid_token",
        "error_details": "Token已过期或无效",
        "retry_allowed": true,
        "max_retry_attempts": 3
    },
    "timestamp": 1640996040,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1001 - TOKEN无效)：**
```json
{
    "code": 1001,
    "message": "TOKEN无效",
    "data": {
        "token_status": "invalid",
        "error_reason": "token_malformed",
        "need_refresh": true,
        "auth_url": "/api/login"
    },
    "timestamp": 1640996040,
    "request_id": "req_abc123_def456"
}
```

---

## 📊 **第四阶段：基础数据接口** (39个接口)

### 📋 **测试目标**
按照用户建议的顺序建立基础数据：多模型选择→图片→角色→音色→音效→音乐→视频，每个类型准备30条测试数据。

### 🤖 **4.1 多模型选择** (7个接口)

#### 步骤1: 2.1 获取可用模型 GET /api/ai-models/available

**请求参数示例：**
```json
{
    "type": "image_generation",
    "capability": "high_quality",
    "page": 1
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "models": [
            {
                "model_id": "liblib_ai_image_v1",
                "model_name": "LiblibAI 专业图像生成",
                "platform": "LiblibAI",
                "type": "image_generation",
                "capabilities": ["high_quality", "comfyui_workflow", "style_transfer", "professional_generation"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 10,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "2048x2048",
                    "max_batch_size": 4,
                    "timeout": 300
                },
                "description": "专业图像生成、ComfyUI工作流、风格转换"
            },
            {
                "model_id": "liblib_ai_character_v1",
                "model_name": "LiblibAI 角色形象生成",
                "platform": "LiblibAI",
                "type": "character_generation",
                "capabilities": ["character_design", "role_image", "character_style"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 12,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1024x1024",
                    "max_batch_size": 2,
                    "timeout": 240
                },
                "description": "角色形象生成、角色设计"
            },
            {
                "model_id": "liblib_ai_style_v1",
                "model_name": "LiblibAI 艺术风格生成",
                "platform": "LiblibAI",
                "type": "style_generation",
                "capabilities": ["art_style", "style_transfer", "style_conversion"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 8,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1536x1536",
                    "max_batch_size": 3,
                    "timeout": 200
                },
                "description": "艺术风格生成、风格转换"
            },
            {
                "model_id": "kling_ai_video_v2",
                "model_name": "KlingAI 专业视频生成",
                "platform": "KlingAI",
                "type": "video_generation",
                "capabilities": ["professional_video", "image_to_video", "video_extension", "high_quality"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 50,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 30,
                    "max_resolution": "1920x1080",
                    "timeout": 1800
                },
                "description": "专业视频生成、图像转视频、视频扩展"
            },
            {
                "model_id": "kling_ai_image_v1",
                "model_name": "KlingAI 高质量图像生成",
                "platform": "KlingAI",
                "type": "image_generation",
                "capabilities": ["high_quality", "image_upscale", "image_repair"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 15,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "2048x2048",
                    "max_batch_size": 3,
                    "timeout": 360
                },
                "description": "高质量图像生成、图像放大、图像修复"
            },
            {
                "model_id": "kling_ai_character_v1",
                "model_name": "KlingAI 角色动画生成",
                "platform": "KlingAI",
                "type": "character_generation",
                "capabilities": ["character_animation", "character_expression", "role_motion"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 25,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 15,
                    "max_resolution": "1024x1024",
                    "timeout": 900
                },
                "description": "角色动画生成、角色表情"
            },
            {
                "model_id": "kling_ai_style_v1",
                "model_name": "KlingAI 视觉风格生成",
                "platform": "KlingAI",
                "type": "style_generation",
                "capabilities": ["visual_style", "style_application", "style_rendering"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 18,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1920x1080",
                    "max_batch_size": 2,
                    "timeout": 450
                },
                "description": "视觉风格生成、风格应用"
            },
            {
                "model_id": "minimax_image_v1",
                "model_name": "MiniMax 多模态图像生成",
                "platform": "MiniMax",
                "type": "image_generation",
                "capabilities": ["multimodal_generation", "image_understanding", "context_aware"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 12,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1920x1920",
                    "max_batch_size": 3,
                    "timeout": 300
                },
                "description": "多模态图像生成、图像理解"
            },
            {
                "model_id": "minimax_video_v1",
                "model_name": "MiniMax 多模态视频生成",
                "platform": "MiniMax",
                "type": "video_generation",
                "capabilities": ["multimodal_video", "video_understanding", "intelligent_editing"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 45,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 25,
                    "max_resolution": "1920x1080",
                    "timeout": 1500
                },
                "description": "多模态视频生成、视频理解"
            },
            {
                "model_id": "minimax_story_v1",
                "model_name": "MiniMax 多模态剧情生成",
                "platform": "MiniMax",
                "type": "story_generation",
                "capabilities": ["multimodal_story", "plot_construction", "narrative_generation"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 20,
                    "currency": "credits"
                },
                "limits": {
                    "max_length": 5000,
                    "max_chapters": 10,
                    "timeout": 600
                },
                "description": "多模态剧情生成、情节构建"
            },
            {
                "model_id": "minimax_character_v1",
                "model_name": "MiniMax 角色属性生成",
                "platform": "MiniMax",
                "type": "character_generation",
                "capabilities": ["character_attributes", "character_dialogue", "personality_design"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 15,
                    "currency": "credits"
                },
                "limits": {
                    "max_attributes": 20,
                    "max_dialogues": 50,
                    "timeout": 400
                },
                "description": "角色属性生成、角色对话"
            },
            {
                "model_id": "minimax_style_v1",
                "model_name": "MiniMax 多模态风格生成",
                "platform": "MiniMax",
                "type": "style_generation",
                "capabilities": ["multimodal_style", "style_understanding", "adaptive_styling"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 14,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1600x1600",
                    "max_batch_size": 2,
                    "timeout": 350
                },
                "description": "多模态风格生成、风格理解"
            },
            {
                "model_id": "minimax_sound_v1",
                "model_name": "MiniMax 多模态音效生成",
                "platform": "MiniMax",
                "type": "sound_generation",
                "capabilities": ["multimodal_sound", "sound_understanding", "audio_synthesis"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 18,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 60,
                    "sample_rate": "48kHz",
                    "timeout": 480
                },
                "description": "多模态音效生成、音效理解"
            },
            {
                "model_id": "minimax_voice_v1",
                "model_name": "MiniMax 音色设计",
                "platform": "MiniMax",
                "type": "voice_generation",
                "capabilities": ["voice_design", "voice_synthesis", "tone_customization"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 22,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 120,
                    "sample_rate": "44.1kHz",
                    "timeout": 600
                },
                "description": "音色设计、音色合成"
            },
            {
                "model_id": "minimax_music_v1",
                "model_name": "MiniMax 专业音乐生成",
                "platform": "MiniMax",
                "type": "music_generation",
                "capabilities": ["professional_music", "music_creation", "music_understanding"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 35,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 300,
                    "sample_rate": "48kHz",
                    "timeout": 1200
                },
                "description": "专业音乐生成、音乐创作、音乐理解"
            },
            {
                "model_id": "deepseek_story_v1",
                "model_name": "DeepSeek 专业剧情创作",
                "platform": "DeepSeek",
                "type": "story_generation",
                "capabilities": ["professional_story", "script_writing", "character_dialogue", "scene_description"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 25,
                    "currency": "credits"
                },
                "limits": {
                    "max_length": 8000,
                    "max_chapters": 15,
                    "timeout": 800
                },
                "description": "专业剧情创作、分镜脚本、角色对话"
            },
            {
                "model_id": "deepseek_script_v1",
                "model_name": "DeepSeek 分镜脚本专家",
                "platform": "DeepSeek",
                "type": "story_generation",
                "capabilities": ["storyboard_script", "scene_planning", "dialogue_optimization"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 30,
                    "currency": "credits"
                },
                "limits": {
                    "max_scenes": 50,
                    "max_dialogues": 200,
                    "timeout": 900
                },
                "description": "分镜脚本专业创作、场景规划"
            },
            {
                "model_id": "volcengine_sound_v1",
                "model_name": "火山引擎豆包 专业音效处理",
                "platform": "火山引擎豆包",
                "type": "sound_generation",
                "capabilities": ["professional_sound", "sound_synthesis", "audio_mixing"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 20,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 180,
                    "sample_rate": "24kHz",
                    "timeout": 720
                },
                "description": "专业音效处理、音效合成"
            },
            {
                "model_id": "volcengine_voice_v1",
                "model_name": "火山引擎豆包 声音复刻",
                "platform": "火山引擎豆包",
                "type": "voice_generation",
                "capabilities": ["voice_cloning", "voice_processing", "tone_adjustment"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 28,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 240,
                    "sample_rate": "24kHz",
                    "timeout": 960
                },
                "description": "声音复刻、音色处理"
            },
            {
                "model_id": "volcengine_tts_v1",
                "model_name": "火山引擎豆包 大模型语音合成",
                "platform": "火山引擎豆包",
                "type": "voice_generation",
                "capabilities": ["advanced_tts", "emotion_synthesis", "multi_speaker"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 15,
                    "currency": "credits"
                },
                "limits": {
                    "max_text_length": 2000,
                    "sample_rate": "24kHz",
                    "timeout": 300
                },
                "description": "大模型24kHz高质量语音合成"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 20,
            "last_page": 1
        },
        "platform_summary": {
            "LiblibAI": 3,
            "KlingAI": 4,
            "MiniMax": 8,
            "DeepSeek": 2,
            "火山引擎豆包": 3
        },
        "business_type_summary": {
            "image_generation": 3,
            "video_generation": 2,
            "story_generation": 3,
            "character_generation": 3,
            "style_generation": 3,
            "sound_generation": 2,
            "voice_generation": 3,
            "music_generation": 1
        },
        "total_platforms": 5,
        "total_business_types": 8
    },
    "timestamp": 1640996100,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996100,
    "request_id": "req_abc123_def456"
}
```