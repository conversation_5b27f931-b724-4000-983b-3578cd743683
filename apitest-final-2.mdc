# API接口测试数据补充完善文档 V2.0

## 📋 **文档概述**
基于 apitest-url.mdc 中的第七阶段接口顺序，补全每个API接口的请求数据和业务状态码响应格式化数据示例代码。

---

## 🔧 **第七阶段：其它功能接口** (187个接口)

### 📋 **测试目标**
验证系统的完整性和扩展功能，按功能模块逐步测试。

### 🎯 **7.1 积分管理系统** (6个接口)

#### 步骤1: 21.1 积分余额查询 GET /api/points/balance

**请求参数示例：**
```json
{
    "user_id": "user_12345",
    "include_frozen": true,
    "include_history": false
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": "user_12345",
        "balance": {
            "available_points": 1250,
            "frozen_points": 200,
            "total_points": 1450,
            "pending_points": 50
        },
        "account_info": {
            "account_level": "premium",
            "credit_limit": 5000,
            "expiry_date": "2024-12-31",
            "last_recharge": "2024-01-01 10:30:00"
        },
        "balance_breakdown": {
            "earned_points": 800,
            "purchased_points": 650,
            "bonus_points": 0,
            "used_points": 3200
        },
        "recent_changes": [
            {
                "change_type": "consumption",
                "amount": -50,
                "description": "AI图像生成",
                "timestamp": "2024-01-01 14:30:00"
            },
            {
                "change_type": "recharge",
                "amount": 500,
                "description": "在线充值",
                "timestamp": "2024-01-01 10:30:00"
            }
        ]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 用户不存在)：**
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": {
        "user_id": "invalid_user_id",
        "suggestion": "请检查用户ID是否正确"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 21.2 积分充值 POST /api/points/recharge

**请求参数示例：**
```json
{
    "amount": 500,
    "payment_method": "alipay",
    "payment_channel": "web",
    "promotion_code": "NEWUSER2024",
    "return_url": "https://example.com/payment/success",
    "notify_url": "https://example.com/payment/notify"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "充值订单创建成功",
    "data": {
        "order_id": "order_20240101_001",
        "user_id": "user_12345",
        "amount": 500,
        "actual_amount": 550,
        "bonus_points": 50,
        "payment_method": "alipay",
        "payment_info": {
            "payment_url": "https://openapi.alipay.com/gateway.do?...",
            "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
            "order_string": "alipay_sdk=alipay-sdk-java-4.22.110.ALL&charset=utf-8&..."
        },
        "promotion_applied": {
            "code": "NEWUSER2024",
            "discount_type": "bonus",
            "discount_value": 50,
            "description": "新用户充值赠送10%积分"
        },
        "order_status": "pending",
        "expire_time": "2024-01-01 15:30:00",
        "created_at": "2024-01-01 15:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "amount": ["充值金额必须大于0", "充值金额不能超过10000"],
            "payment_method": ["支付方式不支持"],
            "promotion_code": ["优惠码已过期"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 21.3 积分交易记录 GET /api/points/transactions

**请求参数示例：**
```json
{
    "user_id": "user_12345",
    "type": "all",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "transactions": [
            {
                "transaction_id": "txn_20240101_001",
                "user_id": "user_12345",
                "type": "consumption",
                "amount": -50,
                "balance_before": 1300,
                "balance_after": 1250,
                "description": "AI图像生成",
                "related_order": "order_img_001",
                "status": "completed",
                "created_at": "2024-01-01 14:30:00"
            },
            {
                "transaction_id": "txn_20240101_002",
                "user_id": "user_12345",
                "type": "recharge",
                "amount": 500,
                "balance_before": 800,
                "balance_after": 1300,
                "description": "在线充值",
                "related_order": "order_20240101_001",
                "status": "completed",
                "created_at": "2024-01-01 10:30:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 45,
            "last_page": 3
        },
        "summary": {
            "total_income": 1500,
            "total_expense": 800,
            "net_change": 700,
            "transaction_count": 45
        }
    },
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 20.1 积分预检查 POST /api/credits/check

**请求参数示例：**
```json
{
    "user_id": "user_12345",
    "operation": "ai_image_generation",
    "required_credits": 50,
    "operation_params": {
        "image_count": 1,
        "quality": "high",
        "platform": "LiblibAI"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "积分检查通过",
    "data": {
        "user_id": "user_12345",
        "current_balance": 1250,
        "required_credits": 50,
        "sufficient": true,
        "remaining_after_operation": 1200,
        "operation_allowed": true,
        "check_details": {
            "operation": "ai_image_generation",
            "base_cost": 40,
            "quality_premium": 10,
            "platform_fee": 0,
            "total_cost": 50
        },
        "recommendations": [
            "当前积分充足，可以执行操作",
            "建议在积分低于100时及时充值"
        ],
        "check_id": "check_20240101_001",
        "valid_until": "2024-01-01 15:05:00"
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 20.2 积分冻结 POST /api/credits/freeze

**请求参数示例：**
```json
{
    "user_id": "user_12345",
    "amount": 100,
    "reason": "pending_ai_task",
    "related_task": "task_img_001",
    "freeze_duration": 1800,
    "auto_release": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "积分冻结成功",
    "data": {
        "freeze_id": "freeze_20240101_001",
        "user_id": "user_12345",
        "frozen_amount": 100,
        "balance_before": 1250,
        "available_balance_after": 1150,
        "frozen_balance_after": 300,
        "reason": "pending_ai_task",
        "related_task": "task_img_001",
        "freeze_duration": 1800,
        "auto_release": true,
        "release_time": "2024-01-01 15:30:00",
        "created_at": "2024-01-01 15:00:00",
        "status": "active"
    },
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 20.3 积分返还 POST /api/credits/refund

**请求参数示例：**
```json
{
    "user_id": "user_12345",
    "transaction_id": "txn_20240101_001",
    "refund_amount": 50,
    "refund_reason": "task_failed",
    "related_task": "task_img_001",
    "admin_notes": "AI生成失败，全额退款"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "积分返还成功",
    "data": {
        "refund_id": "refund_20240101_001",
        "user_id": "user_12345",
        "original_transaction": "txn_20240101_001",
        "refund_amount": 50,
        "balance_before": 1200,
        "balance_after": 1250,
        "refund_reason": "task_failed",
        "related_task": "task_img_001",
        "refund_type": "full_refund",
        "processing_fee": 0,
        "actual_refund": 50,
        "refund_status": "completed",
        "processed_at": "2024-01-01 15:35:00",
        "admin_notes": "AI生成失败，全额退款",
        "refund_transaction_id": "txn_refund_20240101_001"
    },
    "timestamp": 1640995440,
    "request_id": "req_abc123_def456"
}
```

---

### 🎯 **7.2 任务管理系统** (11个接口)

#### 步骤1: 9.1 获取超时配置 GET /api/tasks/timeout-config

**请求参数示例：**
```json
{
    "task_type": "ai_generation",
    "platform": "all"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "timeout_configs": {
            "ai_text_generation": {
                "default_timeout": 60,
                "max_timeout": 300,
                "retry_timeout": 30,
                "platforms": {
                    "OpenAI": 60,
                    "Claude": 90,
                    "Gemini": 45
                }
            },
            "ai_image_generation": {
                "default_timeout": 300,
                "max_timeout": 600,
                "retry_timeout": 120,
                "platforms": {
                    "LiblibAI": 300,
                    "MidjourneyAI": 480,
                    "StableDiffusion": 240
                }
            },
            "ai_video_generation": {
                "default_timeout": 1800,
                "max_timeout": 3600,
                "retry_timeout": 600,
                "platforms": {
                    "KlingAI": 1800,
                    "RunwayML": 2400
                }
            }
        },
        "global_settings": {
            "max_concurrent_tasks": 10,
            "queue_timeout": 3600,
            "cleanup_interval": 300
        },
        "last_updated": "2024-01-01 12:00:00"
    },
    "timestamp": 1640995500,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 26.1 取消任务 POST /api/tasks/{id}/cancel

**请求参数示例：**
```json
{
    "id": "task_img_001",
    "cancel_reason": "user_request",
    "force_cancel": false,
    "refund_credits": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "任务取消成功",
    "data": {
        "task_id": "task_img_001",
        "original_status": "processing",
        "new_status": "cancelled",
        "cancel_reason": "user_request",
        "cancelled_at": "2024-01-01 15:40:00",
        "refund_info": {
            "refund_eligible": true,
            "refund_amount": 50,
            "refund_status": "processing",
            "refund_id": "refund_20240101_002"
        },
        "cleanup_actions": [
            "释放计算资源",
            "清理临时文件",
            "更新任务队列"
        ]
    },
    "timestamp": 1640995560,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 26.2 重试任务 POST /api/tasks/{id}/retry

**请求参数示例：**
```json
{
    "id": "task_img_001",
    "retry_reason": "platform_error",
    "change_platform": true,
    "new_platform": "StableDiffusion",
    "max_retries": 3
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "任务重试成功",
    "data": {
        "task_id": "task_img_001",
        "retry_id": "retry_20240101_001",
        "original_platform": "LiblibAI",
        "new_platform": "StableDiffusion",
        "retry_count": 1,
        "max_retries": 3,
        "retry_reason": "platform_error",
        "new_status": "queued",
        "estimated_completion": "2024-01-01 16:00:00",
        "retry_settings": {
            "timeout": 240,
            "priority": "high",
            "resource_allocation": "standard"
        },
        "created_at": "2024-01-01 15:45:00"
    },
    "timestamp": 1640995620,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 26.3 批量任务状态查询 GET /api/batch/tasks/status

**请求参数示例：**
```json
{
    "task_ids": ["task_img_001", "task_video_001", "task_text_001"],
    "include_details": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "tasks": [
            {
                "task_id": "task_img_001",
                "status": "completed",
                "progress": 100,
                "result_url": "https://example.com/results/task_img_001.jpg",
                "completed_at": "2024-01-01 15:50:00"
            },
            {
                "task_id": "task_video_001",
                "status": "processing",
                "progress": 65,
                "estimated_completion": "2024-01-01 16:15:00"
            },
            {
                "task_id": "task_text_001",
                "status": "failed",
                "error_message": "平台连接超时",
                "failed_at": "2024-01-01 15:35:00"
            }
        ],
        "summary": {
            "total_tasks": 3,
            "completed": 1,
            "processing": 1,
            "failed": 1,
            "queued": 0
        }
    },
    "timestamp": 1640995680,
    "request_id": "req_abc123_def456"
}
```
