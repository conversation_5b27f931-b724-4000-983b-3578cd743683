# API接口测试数据补充完善文档

## 📋 **文档概述**
基于 apitest-url.mdc 中的接口顺序，补全每个API接口的请求数据和业务状态码响应格式化数据示例代码。

---

## 🚀 **第一阶段：用户注册登录** (10个接口)

### 📋 **测试目标**
建立最基础的用户认证体系，确保后续所有接口都有认证基础。

### 🔐 **核心认证流程**

#### 步骤1: 5.1 生成验证码 GET /api/captcha/generate

**请求参数示例：**
```json
{
    "type": "register",
    "phone": "13800138000"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "验证码生成成功",
    "data": {
        "captcha_id": "captcha_abc123_def456",
        "captcha_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expire_time": 300,
        "created_at": "2024-01-01 12:00:00"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "type": ["验证码类型不能为空"],
            "phone": ["手机号格式不正确"]
        }
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1052 - 验证码发送频率限制)：**
```json
{
    "code": 1052,
    "message": "验证码发送频率限制",
    "data": {
        "retry_after": 60,
        "remaining_attempts": 2,
        "reset_time": "2024-01-01 12:01:00"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 5.2 验证验证码 POST /api/captcha/verify

**请求参数示例：**
```json
{
    "captcha_id": "captcha_abc123_def456",
    "code": "1234"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "验证码验证成功",
    "data": {
        "verified": true,
        "captcha_id": "captcha_abc123_def456",
        "verified_at": "2024-01-01 12:00:30"
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "captcha_id": ["验证码ID不能为空"],
            "code": ["验证码不能为空"]
        }
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1050 - 验证码错误)：**
```json
{
    "code": 1050,
    "message": "验证码错误",
    "data": {
        "remaining_attempts": 2,
        "captcha_id": "captcha_abc123_def456",
        "error_count": 1
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1051 - 验证码已过期)：**
```json
{
    "code": 1051,
    "message": "验证码已过期",
    "data": {
        "captcha_id": "captcha_abc123_def456",
        "expired_at": "2024-01-01 11:55:00",
        "need_refresh": true
    },
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995230,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 5.3 刷新验证码 POST /api/captcha/refresh

**请求参数示例：**
```json
{
    "captcha_id": "captcha_abc123_def456"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "验证码刷新成功",
    "data": {
        "captcha_id": "captcha_new123_def456",
        "captcha_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expire_time": 300,
        "created_at": "2024-01-01 12:01:00"
    },
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 验证码不存在)：**
```json
{
    "code": 404,
    "message": "验证码不存在",
    "data": {
        "captcha_id": "captcha_abc123_def456",
        "error_type": "not_found"
    },
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "captcha_id": ["验证码ID不能为空"]
        }
    },
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995260,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 4.1 用户注册 POST /api/register

**请求参数示例：**
```json
{
    "username": "testuser",
    "password": "password123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "verification_code": "1234",
    "invite_code": "INV123456",
    "agree_terms": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "status": "active",
        "created_at": "2024-01-01 12:02:00",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_expires_at": "2024-02-01 12:02:00"
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "username": ["用户名已存在"],
            "password": ["密码长度至少6位"],
            "email": ["邮箱格式不正确"],
            "phone": ["手机号格式不正确"],
            "verification_code": ["验证码不能为空"],
            "agree_terms": ["必须同意服务条款"]
        }
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1002 - 用户已存在)：**
```json
{
    "code": 1002,
    "message": "用户已存在",
    "data": {
        "username": "testuser",
        "conflict_field": "username",
        "existing_user_id": 12344
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1009 - 邮箱已存在)：**
```json
{
    "code": 1009,
    "message": "邮箱已存在",
    "data": {
        "email": "<EMAIL>",
        "conflict_field": "email",
        "existing_user_id": 12344
    },
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995320,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 4.2 用户登录 POST /api/login

**请求参数示例：**
```json
{
    "login": "testuser",
    "password": "password123",
    "device_info": {
        "device_type": "web",
        "device_id": "device_abc123",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "ip_address": "*************"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "https://example.com/avatar/12345.jpg",
        "status": "active",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "refresh_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_expires_at": "2024-02-01 12:03:00",
        "last_login_at": "2024-01-01 12:03:00",
        "permissions": ["user.basic", "user.create", "user.read"]
    },
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1001 - 用户名或密码错误)：**
```json
{
    "code": 1001,
    "message": "用户名或密码错误",
    "data": {
        "login_attempts": 3,
        "remaining_attempts": 2,
        "lockout_time": null
    },
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1003 - 账户被锁定)：**
```json
{
    "code": 1003,
    "message": "账户被锁定",
    "data": {
        "lockout_reason": "too_many_failed_attempts",
        "locked_until": "2024-01-01 13:03:00",
        "remaining_time": 3600,
        "contact_support": true
    },
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995380,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 4.7 验证Token GET /api/verify

**请求参数示例：**
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "Token验证成功",
    "data": {
        "valid": true,
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "token_expires_at": "2024-02-01 12:03:00",
        "remaining_time": 2592000,
        "permissions": ["user.basic", "user.create", "user.read"]
    },
    "timestamp": 1640995440,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - Token无效或过期)：**
```json
{
    "code": 401,
    "message": "Token无效或过期",
    "data": {
        "valid": false,
        "error_type": "token_expired",
        "expired_at": "2024-01-01 11:03:00",
        "need_refresh": true
    },
    "timestamp": 1640995440,
    "request_id": "req_abc123_def456"
}
```

#### 步骤7: 4.4 刷新Token POST /api/refresh

**请求参数示例：**
```json
{
    "refresh_token": "refresh_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9_new...",
        "refresh_token": "refresh_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9_new...",
        "token_expires_at": "2024-02-01 12:04:00",
        "refreshed_at": "2024-01-01 12:04:00"
    },
    "timestamp": 1640995500,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - refresh_token无效或过期)：**
```json
{
    "code": 401,
    "message": "refresh_token无效或过期",
    "data": {
        "error_type": "refresh_token_expired",
        "need_relogin": true,
        "expired_at": "2024-01-01 11:04:00"
    },
    "timestamp": 1640995500,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "refresh_token": ["刷新令牌不能为空"]
        }
    },
    "timestamp": 1640995500,
    "request_id": "req_abc123_def456"
}
```

#### 步骤8: 4.3 用户登出 POST /api/logout

**请求参数示例：**
```json
{
    "all_devices": false
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "登出成功",
    "data": {
        "user_id": 12345,
        "logout_type": "single_device",
        "logged_out_at": "2024-01-01 12:05:00",
        "token_invalidated": true
    },
    "timestamp": 1640995560,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995560,
    "request_id": "req_abc123_def456"
}
```

#### 步骤9: 4.5 忘记密码 POST /api/forgot-password

**请求参数示例：**
```json
{
    "email": "<EMAIL>"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "密码重置邮件发送成功",
    "data": {
        "email": "<EMAIL>",
        "reset_token_expires_at": "2024-01-01 13:06:00",
        "sent_at": "2024-01-01 12:06:00",
        "email_masked": "te**@example.com"
    },
    "timestamp": 1640995620,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 用户不存在)：**
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": {
        "email": "<EMAIL>",
        "error_type": "user_not_found"
    },
    "timestamp": 1640995620,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "email": ["邮箱格式不正确"]
        }
    },
    "timestamp": 1640995620,
    "request_id": "req_abc123_def456"
}
```

#### 步骤10: 4.6 重置密码 POST /api/reset-password

**请求参数示例：**
```json
{
    "token": "reset_token_abc123_def456",
    "new_password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "密码重置成功",
    "data": {
        "user_id": 12345,
        "email": "<EMAIL>",
        "reset_at": "2024-01-01 12:07:00",
        "password_updated": true,
        "auto_login": false
    },
    "timestamp": 1640995680,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "token": ["重置令牌不能为空"],
            "new_password": ["密码长度至少6位"],
            "password_confirmation": ["两次密码输入不一致"]
        }
    },
    "timestamp": 1640995680,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1004 - 重置token无效或过期)：**
```json
{
    "code": 1004,
    "message": "重置token无效或过期",
    "data": {
        "token": "reset_token_abc123_def456",
        "error_type": "token_expired",
        "expired_at": "2024-01-01 11:07:00",
        "need_request_new": true
    },
    "timestamp": 1640995680,
    "request_id": "req_abc123_def456"
}
```

---

## 🔐 **第二阶段：用户安全验证** (4个接口)

### 📋 **测试目标**
验证登录用户的基本信息获取和权限查询，确保用户可以正常使用系统。

### 👤 **用户基础信息验证**

#### 步骤1: 24.1 用户中心信息 GET /api/user/profile

**请求参数示例：**
```json
{}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "https://example.com/avatar/12345.jpg",
        "nickname": "测试用户",
        "gender": "male",
        "birthday": "1990-01-01",
        "location": "北京市",
        "bio": "这是一个测试用户",
        "status": "active",
        "level": 5,
        "points": 1500,
        "credits": 2000,
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 12:07:00",
        "last_login_at": "2024-01-01 12:03:00",
        "email_verified": true,
        "phone_verified": true
    },
    "timestamp": 1640995740,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995740,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 23.1 获取用户权限 GET /api/permissions/user

**请求参数示例：**
```json
{
    "user_id": 12345
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "roles": [
            {
                "role_id": 1,
                "role_name": "普通用户",
                "role_code": "user",
                "assigned_at": "2024-01-01 10:00:00"
            }
        ],
        "permissions": [
            {
                "permission_id": 1,
                "permission_name": "基础权限",
                "permission_code": "user.basic",
                "resource": "*",
                "actions": ["read"]
            },
            {
                "permission_id": 2,
                "permission_name": "创建权限",
                "permission_code": "user.create",
                "resource": "projects",
                "actions": ["create", "read", "update"]
            }
        ],
        "permission_summary": {
            "total_permissions": 2,
            "active_permissions": 2,
            "last_updated": "2024-01-01 10:00:00"
        }
    },
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 用户不存在)：**
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": {
        "user_id": 12345,
        "error_type": "user_not_found"
    },
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (403 - 无权限)：**
```json
{
    "code": 403,
    "message": "无权限访问",
    "data": {
        "required_permission": "admin.user.read",
        "current_permissions": ["user.basic", "user.create"]
    },
    "timestamp": 1640995800,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 23.2 检查用户权限 POST /api/permissions/check

**请求参数示例：**
```json
{
    "permission": "user.create",
    "resource_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "权限检查完成",
    "data": {
        "user_id": 12345,
        "permission": "user.create",
        "resource_id": "project_123",
        "has_permission": true,
        "permission_source": "role",
        "role_name": "普通用户",
        "checked_at": "2024-01-01 12:08:00",
        "expires_at": null
    },
    "timestamp": 1640995860,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995860,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 24.4 获取用户偏好设置 GET /api/user/preferences

**请求参数示例：**
```json
{}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "preferences": {
            "language": "zh-CN",
            "timezone": "Asia/Shanghai",
            "theme": "light",
            "notifications": {
                "email_enabled": true,
                "push_enabled": true,
                "sms_enabled": false,
                "marketing_enabled": false
            },
            "ai_settings": {
                "preferred_platforms": ["LiblibAI", "KlingAI", "MiniMax"],
                "default_quality": "high",
                "auto_retry": true,
                "timeout_preference": "standard"
            },
            "privacy": {
                "profile_visibility": "public",
                "work_visibility": "friends",
                "activity_tracking": true
            },
            "interface": {
                "sidebar_collapsed": false,
                "grid_view": true,
                "items_per_page": 20
            }
        },
        "last_updated": "2024-01-01 12:00:00",
        "version": 1
    },
    "timestamp": 1640995920,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995920,
    "request_id": "req_abc123_def456"
}
```

---

## 🌐 **第三阶段：WebSocket安全验证** (2个接口)

### 📋 **测试目标**
验证WebSocket服务的基础状态和认证机制，确保Python工具可以正常连接。

### 🔌 **WebSocket基础验证**

#### 步骤1: 7.4 WebSocket服务状态 GET /api/websocket/status

**请求参数示例：**
```json
{}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "service_status": "running",
        "server_info": {
            "host": "0.0.0.0",
            "port": 8080,
            "protocol": "wss",
            "started_at": "2024-01-01 10:00:00",
            "uptime": 7200
        },
        "connection_stats": {
            "total_connections": 150,
            "active_connections": 120,
            "python_tool_connections": 45,
            "web_tool_connections": 0
        },
        "performance": {
            "cpu_usage": 15.5,
            "memory_usage": 256,
            "message_rate": 1200,
            "error_rate": 0.01
        },
        "version": "1.0.0",
        "last_check": "2024-01-01 12:09:00"
    },
    "timestamp": 1640995980,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995980,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1012 - 服务不可用)：**
```json
{
    "code": 1012,
    "message": "WebSocket服务不可用",
    "data": {
        "service_status": "stopped",
        "error_reason": "service_crashed",
        "last_error": "Connection refused",
        "restart_required": true,
        "estimated_recovery_time": 300
    },
    "timestamp": 1640995980,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 7.1 WebSocket连接认证 POST /api/websocket/auth

**请求参数示例：**
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "channel": "python_tool_channel",
    "client_info": {
        "client_type": "python_tool",
        "client_version": "1.0.0",
        "user_agent": "PythonTool/1.0.0",
        "api_key": "encrypted_api_key_abc123"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "认证成功",
    "data": {
        "auth_success": true,
        "user_id": 12345,
        "username": "testuser",
        "channel": "python_tool_channel",
        "connection_id": "conn_abc123_def456",
        "websocket_url": "wss://api.tiptop.cn:8080/websocket",
        "auth_token": "ws_auth_token_abc123_def456",
        "expires_at": "2024-01-01 13:10:00",
        "permissions": ["websocket.connect", "websocket.receive", "websocket.send"],
        "rate_limits": {
            "messages_per_minute": 1000,
            "max_connections": 5
        },
        "authenticated_at": "2024-01-01 12:10:00"
    },
    "timestamp": 1640996040,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 认证失败)：**
```json
{
    "code": 401,
    "message": "WebSocket认证失败",
    "data": {
        "auth_success": false,
        "error_type": "invalid_token",
        "error_details": "Token已过期或无效",
        "retry_allowed": true,
        "max_retry_attempts": 3
    },
    "timestamp": 1640996040,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1001 - TOKEN无效)：**
```json
{
    "code": 1001,
    "message": "TOKEN无效",
    "data": {
        "token_status": "invalid",
        "error_reason": "token_malformed",
        "need_refresh": true,
        "auth_url": "/api/login"
    },
    "timestamp": 1640996040,
    "request_id": "req_abc123_def456"
}
```

---

## 📊 **第四阶段：基础数据接口** (39个接口)

### 📋 **测试目标**
按照用户建议的顺序建立基础数据：多模型选择→图片→角色→音色→音效→音乐→视频，每个类型准备30条测试数据。

### 🤖 **4.1 多模型选择** (7个接口)

#### 步骤1: 2.1 获取可用模型 GET /api/ai-models/available

**请求参数示例：**
```json
{
    "type": "image_generation",
    "capability": "high_quality",
    "page": 1
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "models": [
            {
                "model_id": "liblib_ai_image_v1",
                "model_name": "LiblibAI 专业图像生成",
                "platform": "LiblibAI",
                "type": "image_generation",
                "capabilities": ["high_quality", "comfyui_workflow", "style_transfer", "professional_generation"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 10,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "2048x2048",
                    "max_batch_size": 4,
                    "timeout": 300
                },
                "description": "专业图像生成、ComfyUI工作流、风格转换"
            },
            {
                "model_id": "liblib_ai_character_v1",
                "model_name": "LiblibAI 角色形象生成",
                "platform": "LiblibAI",
                "type": "character_generation",
                "capabilities": ["character_design", "role_image", "character_style"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 12,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1024x1024",
                    "max_batch_size": 2,
                    "timeout": 240
                },
                "description": "角色形象生成、角色设计"
            },
            {
                "model_id": "liblib_ai_style_v1",
                "model_name": "LiblibAI 艺术风格生成",
                "platform": "LiblibAI",
                "type": "style_generation",
                "capabilities": ["art_style", "style_transfer", "style_conversion"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 8,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1536x1536",
                    "max_batch_size": 3,
                    "timeout": 200
                },
                "description": "艺术风格生成、风格转换"
            },
            {
                "model_id": "kling_ai_video_v2",
                "model_name": "KlingAI 专业视频生成",
                "platform": "KlingAI",
                "type": "video_generation",
                "capabilities": ["professional_video", "image_to_video", "video_extension", "high_quality"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 50,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 30,
                    "max_resolution": "1920x1080",
                    "timeout": 1800
                },
                "description": "专业视频生成、图像转视频、视频扩展"
            },
            {
                "model_id": "kling_ai_image_v1",
                "model_name": "KlingAI 高质量图像生成",
                "platform": "KlingAI",
                "type": "image_generation",
                "capabilities": ["high_quality", "image_upscale", "image_repair"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 15,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "2048x2048",
                    "max_batch_size": 3,
                    "timeout": 360
                },
                "description": "高质量图像生成、图像放大、图像修复"
            },
            {
                "model_id": "kling_ai_character_v1",
                "model_name": "KlingAI 角色动画生成",
                "platform": "KlingAI",
                "type": "character_generation",
                "capabilities": ["character_animation", "character_expression", "role_motion"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 25,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 15,
                    "max_resolution": "1024x1024",
                    "timeout": 900
                },
                "description": "角色动画生成、角色表情"
            },
            {
                "model_id": "kling_ai_style_v1",
                "model_name": "KlingAI 视觉风格生成",
                "platform": "KlingAI",
                "type": "style_generation",
                "capabilities": ["visual_style", "style_application", "style_rendering"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 18,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1920x1080",
                    "max_batch_size": 2,
                    "timeout": 450
                },
                "description": "视觉风格生成、风格应用"
            },
            {
                "model_id": "minimax_image_v1",
                "model_name": "MiniMax 多模态图像生成",
                "platform": "MiniMax",
                "type": "image_generation",
                "capabilities": ["multimodal_generation", "image_understanding", "context_aware"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 12,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1920x1920",
                    "max_batch_size": 3,
                    "timeout": 300
                },
                "description": "多模态图像生成、图像理解"
            },
            {
                "model_id": "minimax_video_v1",
                "model_name": "MiniMax 多模态视频生成",
                "platform": "MiniMax",
                "type": "video_generation",
                "capabilities": ["multimodal_video", "video_understanding", "intelligent_editing"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 45,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 25,
                    "max_resolution": "1920x1080",
                    "timeout": 1500
                },
                "description": "多模态视频生成、视频理解"
            },
            {
                "model_id": "minimax_story_v1",
                "model_name": "MiniMax 多模态剧情生成",
                "platform": "MiniMax",
                "type": "story_generation",
                "capabilities": ["multimodal_story", "plot_construction", "narrative_generation"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 20,
                    "currency": "credits"
                },
                "limits": {
                    "max_length": 5000,
                    "max_chapters": 10,
                    "timeout": 600
                },
                "description": "多模态剧情生成、情节构建"
            },
            {
                "model_id": "minimax_character_v1",
                "model_name": "MiniMax 角色属性生成",
                "platform": "MiniMax",
                "type": "character_generation",
                "capabilities": ["character_attributes", "character_dialogue", "personality_design"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 15,
                    "currency": "credits"
                },
                "limits": {
                    "max_attributes": 20,
                    "max_dialogues": 50,
                    "timeout": 400
                },
                "description": "角色属性生成、角色对话"
            },
            {
                "model_id": "minimax_style_v1",
                "model_name": "MiniMax 多模态风格生成",
                "platform": "MiniMax",
                "type": "style_generation",
                "capabilities": ["multimodal_style", "style_understanding", "adaptive_styling"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 14,
                    "currency": "credits"
                },
                "limits": {
                    "max_resolution": "1600x1600",
                    "max_batch_size": 2,
                    "timeout": 350
                },
                "description": "多模态风格生成、风格理解"
            },
            {
                "model_id": "minimax_sound_v1",
                "model_name": "MiniMax 多模态音效生成",
                "platform": "MiniMax",
                "type": "sound_generation",
                "capabilities": ["multimodal_sound", "sound_understanding", "audio_synthesis"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 18,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 60,
                    "sample_rate": "48kHz",
                    "timeout": 480
                },
                "description": "多模态音效生成、音效理解"
            },
            {
                "model_id": "minimax_voice_v1",
                "model_name": "MiniMax 音色设计",
                "platform": "MiniMax",
                "type": "voice_generation",
                "capabilities": ["voice_design", "voice_synthesis", "tone_customization"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 22,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 120,
                    "sample_rate": "44.1kHz",
                    "timeout": 600
                },
                "description": "音色设计、音色合成"
            },
            {
                "model_id": "minimax_music_v1",
                "model_name": "MiniMax 专业音乐生成",
                "platform": "MiniMax",
                "type": "music_generation",
                "capabilities": ["professional_music", "music_creation", "music_understanding"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 35,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 300,
                    "sample_rate": "48kHz",
                    "timeout": 1200
                },
                "description": "专业音乐生成、音乐创作、音乐理解"
            },
            {
                "model_id": "deepseek_story_v1",
                "model_name": "DeepSeek 专业剧情创作",
                "platform": "DeepSeek",
                "type": "story_generation",
                "capabilities": ["professional_story", "script_writing", "character_dialogue", "scene_description"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 25,
                    "currency": "credits"
                },
                "limits": {
                    "max_length": 8000,
                    "max_chapters": 15,
                    "timeout": 800
                },
                "description": "专业剧情创作、分镜脚本、角色对话"
            },
            {
                "model_id": "deepseek_script_v1",
                "model_name": "DeepSeek 分镜脚本专家",
                "platform": "DeepSeek",
                "type": "story_generation",
                "capabilities": ["storyboard_script", "scene_planning", "dialogue_optimization"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 30,
                    "currency": "credits"
                },
                "limits": {
                    "max_scenes": 50,
                    "max_dialogues": 200,
                    "timeout": 900
                },
                "description": "分镜脚本专业创作、场景规划"
            },
            {
                "model_id": "volcengine_sound_v1",
                "model_name": "火山引擎豆包 专业音效处理",
                "platform": "火山引擎豆包",
                "type": "sound_generation",
                "capabilities": ["professional_sound", "sound_synthesis", "audio_mixing"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 20,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 180,
                    "sample_rate": "24kHz",
                    "timeout": 720
                },
                "description": "专业音效处理、音效合成"
            },
            {
                "model_id": "volcengine_voice_v1",
                "model_name": "火山引擎豆包 声音复刻",
                "platform": "火山引擎豆包",
                "type": "voice_generation",
                "capabilities": ["voice_cloning", "voice_processing", "tone_adjustment"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 28,
                    "currency": "credits"
                },
                "limits": {
                    "max_duration": 240,
                    "sample_rate": "24kHz",
                    "timeout": 960
                },
                "description": "声音复刻、音色处理"
            },
            {
                "model_id": "volcengine_tts_v1",
                "model_name": "火山引擎豆包 大模型语音合成",
                "platform": "火山引擎豆包",
                "type": "voice_generation",
                "capabilities": ["advanced_tts", "emotion_synthesis", "multi_speaker"],
                "status": "available",
                "pricing": {
                    "credits_per_request": 15,
                    "currency": "credits"
                },
                "limits": {
                    "max_text_length": 2000,
                    "sample_rate": "24kHz",
                    "timeout": 300
                },
                "description": "大模型24kHz高质量语音合成"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 20,
            "last_page": 1
        },
        "platform_summary": {
            "LiblibAI": 3,
            "KlingAI": 4,
            "MiniMax": 8,
            "DeepSeek": 2,
            "火山引擎豆包": 3
        },
        "business_type_summary": {
            "image_generation": 3,
            "video_generation": 2,
            "story_generation": 3,
            "character_generation": 3,
            "style_generation": 3,
            "sound_generation": 2,
            "voice_generation": 3,
            "music_generation": 1
        },
        "total_platforms": 5,
        "total_business_types": 8
    },
    "timestamp": 1640996100,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996100,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 2.2 获取模型详情 GET /api/ai-models/{model_id}/detail

**请求参数示例：**
```json
{
    "model_id": "minimax_image_v1",
    "include_pricing": true,
    "include_examples": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "model_id": "minimax_image_v1",
        "model_name": "MiniMax 多模态图像生成",
        "platform": "MiniMax",
        "type": "image_generation",
        "version": "1.0.0",
        "status": "available",
        "description": "多模态图像生成、图像理解",
        "detailed_description": "MiniMax多模态图像生成模型，支持文本到图像、图像到图像等多种生成方式，具备强大的图像理解和上下文感知能力。",
        "capabilities": [
            {
                "name": "multimodal_generation",
                "description": "多模态生成能力",
                "supported": true
            },
            {
                "name": "image_understanding",
                "description": "图像理解能力",
                "supported": true
            },
            {
                "name": "context_aware",
                "description": "上下文感知",
                "supported": true
            }
        ],
        "pricing": {
            "credits_per_request": 12,
            "currency": "credits",
            "billing_model": "per_request",
            "discount_tiers": [
                {
                    "min_requests": 100,
                    "discount_rate": 0.1,
                    "credits_per_request": 10.8
                },
                {
                    "min_requests": 500,
                    "discount_rate": 0.2,
                    "credits_per_request": 9.6
                }
            ]
        },
        "limits": {
            "max_resolution": "1920x1920",
            "min_resolution": "256x256",
            "max_batch_size": 3,
            "timeout": 300,
            "rate_limit": {
                "requests_per_minute": 60,
                "requests_per_hour": 1000
            }
        },
        "supported_formats": {
            "input": ["text", "image", "json"],
            "output": ["png", "jpg", "webp"]
        },
        "parameters": {
            "required": [
                {
                    "name": "prompt",
                    "type": "string",
                    "description": "生成提示词",
                    "max_length": 2000
                }
            ],
            "optional": [
                {
                    "name": "style",
                    "type": "string",
                    "description": "图像风格",
                    "options": ["realistic", "anime", "artistic", "cartoon"]
                },
                {
                    "name": "quality",
                    "type": "string",
                    "description": "生成质量",
                    "options": ["standard", "high", "ultra"],
                    "default": "high"
                }
            ]
        },
        "examples": [
            {
                "title": "风景图像生成",
                "prompt": "美丽的山水风景，夕阳西下，湖水倒影",
                "parameters": {
                    "style": "realistic",
                    "quality": "high"
                },
                "result_url": "https://example.com/examples/landscape.jpg"
            }
        ],
        "performance_metrics": {
            "average_generation_time": 25.5,
            "success_rate": 0.98,
            "user_satisfaction": 4.7
        },
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 12:00:00"
    },
    "timestamp": 1640996160,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 模型不存在)：**
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": {
        "model_id": "invalid_model_id",
        "available_models": ["minimax_image_v1", "liblib_ai_image_v1", "kling_ai_video_v2"],
        "suggestion": "请检查模型ID是否正确"
    },
    "timestamp": 1640996160,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1011 - 模型不可用)：**
```json
{
    "code": 1011,
    "message": "模型暂时不可用",
    "data": {
        "model_id": "minimax_image_v1",
        "status": "maintenance",
        "reason": "系统维护中",
        "estimated_recovery_time": "2024-01-01 14:00:00",
        "alternative_models": ["liblib_ai_image_v1", "kling_ai_image_v1"]
    },
    "timestamp": 1640996160,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996160,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 2.3 获取收藏模型 GET /api/ai-models/favorites

**请求参数示例：**
```json
{
    "page": 1,
    "per_page": 10,
    "type": "image_generation"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "favorites": [
            {
                "favorite_id": "fav_123456",
                "model_id": "minimax_image_v1",
                "model_name": "MiniMax 多模态图像生成",
                "platform": "MiniMax",
                "type": "image_generation",
                "status": "available",
                "favorited_at": "2024-01-01 10:30:00",
                "usage_count": 25,
                "last_used_at": "2024-01-01 11:45:00",
                "tags": ["常用", "高质量", "多模态"],
                "personal_note": "生成效果很好，经常使用"
            },
            {
                "favorite_id": "fav_123457",
                "model_id": "liblib_ai_image_v1",
                "model_name": "LiblibAI 专业图像生成",
                "platform": "LiblibAI",
                "type": "image_generation",
                "status": "available",
                "favorited_at": "2024-01-01 09:15:00",
                "usage_count": 18,
                "last_used_at": "2024-01-01 10:20:00",
                "tags": ["专业", "ComfyUI"],
                "personal_note": "ComfyUI工作流很强大"
            },
            {
                "favorite_id": "fav_123458",
                "model_id": "kling_ai_video_v2",
                "model_name": "KlingAI 专业视频生成",
                "platform": "KlingAI",
                "type": "video_generation",
                "status": "available",
                "favorited_at": "2024-01-01 08:00:00",
                "usage_count": 12,
                "last_used_at": "2024-01-01 09:30:00",
                "tags": ["视频", "专业"],
                "personal_note": "视频生成质量很高"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 10,
            "total": 8,
            "last_page": 1
        },
        "statistics": {
            "total_favorites": 8,
            "most_used_platform": "MiniMax",
            "most_used_type": "image_generation",
            "total_usage_count": 67
        }
    },
    "timestamp": 1640996220,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "page": ["页码必须大于0"],
            "per_page": ["每页数量必须在1-50之间"],
            "type": ["业务类型不存在"]
        }
    },
    "timestamp": 1640996220,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996220,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 2.4 模型列表 GET /api/ai-models/list

**请求参数示例：**
```json
{
    "platform": "MiniMax",
    "type": "image_generation",
    "status": "available",
    "page": 1,
    "per_page": 20,
    "sort_by": "popularity",
    "sort_order": "desc"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "models": [
            {
                "model_id": "minimax_image_v1",
                "model_name": "MiniMax 多模态图像生成",
                "platform": "MiniMax",
                "type": "image_generation",
                "status": "available",
                "popularity_score": 95,
                "usage_count": 15420,
                "success_rate": 0.98,
                "average_rating": 4.8,
                "pricing": {
                    "credits_per_request": 12,
                    "currency": "credits"
                },
                "capabilities": ["multimodal_generation", "image_understanding", "context_aware"],
                "description": "多模态图像生成、图像理解",
                "is_favorite": true,
                "created_at": "2024-01-01 10:00:00"
            },
            {
                "model_id": "minimax_video_v1",
                "model_name": "MiniMax 多模态视频生成",
                "platform": "MiniMax",
                "type": "video_generation",
                "status": "available",
                "popularity_score": 88,
                "usage_count": 8750,
                "success_rate": 0.96,
                "average_rating": 4.6,
                "pricing": {
                    "credits_per_request": 45,
                    "currency": "credits"
                },
                "capabilities": ["multimodal_video", "video_understanding", "intelligent_editing"],
                "description": "多模态视频生成、视频理解",
                "is_favorite": false,
                "created_at": "2024-01-01 10:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 8,
            "last_page": 1
        },
        "filters": {
            "available_platforms": ["LiblibAI", "KlingAI", "MiniMax", "DeepSeek", "火山引擎豆包"],
            "available_types": ["image_generation", "video_generation", "story_generation", "character_generation", "style_generation", "sound_generation", "voice_generation", "music_generation"],
            "available_statuses": ["available", "maintenance", "deprecated"]
        },
        "sort_options": [
            {"value": "popularity", "label": "热度排序"},
            {"value": "created_at", "label": "创建时间"},
            {"value": "usage_count", "label": "使用次数"},
            {"value": "rating", "label": "用户评分"}
        ]
    },
    "timestamp": 1640996280,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "platform": ["平台不存在"],
            "type": ["业务类型不支持"],
            "sort_by": ["排序字段无效"],
            "sort_order": ["排序方向必须是asc或desc"]
        }
    },
    "timestamp": 1640996280,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996280,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 2.5 智能平台切换 POST /api/ai-models/switch

**请求参数示例：**
```json
{
    "current_model_id": "minimax_image_v1",
    "target_type": "image_generation",
    "switch_reason": "performance",
    "requirements": {
        "max_credits": 15,
        "min_success_rate": 0.95,
        "preferred_capabilities": ["high_quality", "fast_generation"]
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "平台切换建议生成成功",
    "data": {
        "current_model": {
            "model_id": "minimax_image_v1",
            "model_name": "MiniMax 多模态图像生成",
            "platform": "MiniMax",
            "performance_score": 85
        },
        "recommended_models": [
            {
                "model_id": "liblib_ai_image_v1",
                "model_name": "LiblibAI 专业图像生成",
                "platform": "LiblibAI",
                "match_score": 92,
                "switch_benefits": [
                    "更低的积分消耗 (10 vs 12)",
                    "更高的成功率 (0.99 vs 0.98)",
                    "支持ComfyUI工作流"
                ],
                "pricing": {
                    "credits_per_request": 10,
                    "savings_per_request": 2
                },
                "performance_metrics": {
                    "success_rate": 0.99,
                    "average_generation_time": 22.3,
                    "user_satisfaction": 4.9
                },
                "migration_difficulty": "easy",
                "compatibility_score": 0.95
            },
            {
                "model_id": "kling_ai_image_v1",
                "model_name": "KlingAI 高质量图像生成",
                "platform": "KlingAI",
                "match_score": 88,
                "switch_benefits": [
                    "更高的图像质量",
                    "支持图像放大和修复",
                    "更快的生成速度"
                ],
                "pricing": {
                    "credits_per_request": 15,
                    "additional_cost_per_request": 3
                },
                "performance_metrics": {
                    "success_rate": 0.97,
                    "average_generation_time": 18.5,
                    "user_satisfaction": 4.7
                },
                "migration_difficulty": "medium",
                "compatibility_score": 0.88
            }
        ],
        "switch_analysis": {
            "total_candidates": 5,
            "filtered_candidates": 2,
            "best_match": "liblib_ai_image_v1",
            "estimated_savings": {
                "credits_per_month": 240,
                "percentage": 16.7
            },
            "risk_assessment": "low",
            "migration_timeline": "immediate"
        },
        "switch_guide": {
            "preparation_steps": [
                "备份当前模型配置",
                "测试新模型兼容性",
                "准备切换脚本"
            ],
            "rollback_plan": "保留原模型配置7天，支持一键回滚"
        }
    },
    "timestamp": 1640996340,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 当前模型不存在)：**
```json
{
    "code": 404,
    "message": "当前模型不存在",
    "data": {
        "current_model_id": "invalid_model_id",
        "available_models": ["minimax_image_v1", "liblib_ai_image_v1", "kling_ai_image_v1"]
    },
    "timestamp": 1640996340,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1013 - 无可用替代模型)：**
```json
{
    "code": 1013,
    "message": "无可用替代模型",
    "data": {
        "current_model_id": "minimax_image_v1",
        "target_type": "image_generation",
        "requirements": {
            "max_credits": 5,
            "min_success_rate": 0.99
        },
        "reason": "没有模型满足指定的积分和成功率要求",
        "suggestion": "请放宽筛选条件或考虑其他业务类型"
    },
    "timestamp": 1640996340,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996340,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 2.6 平台性能对比 GET /api/ai-models/platform-comparison

**请求参数示例：**
```json
{
    "platforms": ["LiblibAI", "KlingAI", "MiniMax"],
    "business_type": "image_generation",
    "comparison_metrics": ["performance", "pricing", "success_rate", "user_satisfaction"],
    "time_range": "last_30_days"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "comparison_summary": {
            "platforms_count": 3,
            "business_type": "image_generation",
            "comparison_date": "2024-01-01 12:15:00",
            "data_period": "2023-12-02 - 2024-01-01"
        },
        "platform_comparison": [
            {
                "platform": "LiblibAI",
                "overall_score": 92,
                "rank": 1,
                "models_count": 3,
                "metrics": {
                    "performance": {
                        "average_generation_time": 22.3,
                        "success_rate": 0.99,
                        "score": 95
                    },
                    "pricing": {
                        "average_credits_per_request": 10,
                        "cost_effectiveness": 0.92,
                        "score": 90
                    },
                    "user_satisfaction": {
                        "average_rating": 4.9,
                        "total_reviews": 1250,
                        "score": 98
                    },
                    "reliability": {
                        "uptime": 0.998,
                        "error_rate": 0.01,
                        "score": 95
                    }
                },
                "strengths": ["高成功率", "用户满意度高", "ComfyUI工作流支持"],
                "weaknesses": ["模型数量相对较少"],
                "best_use_cases": ["专业图像生成", "工作流自动化"]
            },
            {
                "platform": "MiniMax",
                "overall_score": 88,
                "rank": 2,
                "models_count": 8,
                "metrics": {
                    "performance": {
                        "average_generation_time": 25.5,
                        "success_rate": 0.98,
                        "score": 90
                    },
                    "pricing": {
                        "average_credits_per_request": 18,
                        "cost_effectiveness": 0.85,
                        "score": 82
                    },
                    "user_satisfaction": {
                        "average_rating": 4.7,
                        "total_reviews": 2100,
                        "score": 94
                    },
                    "reliability": {
                        "uptime": 0.996,
                        "error_rate": 0.02,
                        "score": 92
                    }
                },
                "strengths": ["多模态支持", "模型种类丰富", "功能全面"],
                "weaknesses": ["价格相对较高"],
                "best_use_cases": ["多模态生成", "综合AI应用"]
            },
            {
                "platform": "KlingAI",
                "overall_score": 85,
                "rank": 3,
                "models_count": 4,
                "metrics": {
                    "performance": {
                        "average_generation_time": 18.5,
                        "success_rate": 0.97,
                        "score": 88
                    },
                    "pricing": {
                        "average_credits_per_request": 22,
                        "cost_effectiveness": 0.78,
                        "score": 75
                    },
                    "user_satisfaction": {
                        "average_rating": 4.6,
                        "total_reviews": 890,
                        "score": 92
                    },
                    "reliability": {
                        "uptime": 0.995,
                        "error_rate": 0.03,
                        "score": 90
                    }
                },
                "strengths": ["生成速度快", "图像质量高", "视频生成专业"],
                "weaknesses": ["价格较高", "成功率有待提升"],
                "best_use_cases": ["高质量图像生成", "专业视频制作"]
            }
        ],
        "detailed_analysis": {
            "performance_leader": "KlingAI",
            "cost_leader": "LiblibAI",
            "satisfaction_leader": "LiblibAI",
            "reliability_leader": "LiblibAI",
            "overall_recommendation": "LiblibAI",
            "market_trends": {
                "growing_platforms": ["MiniMax"],
                "declining_platforms": [],
                "emerging_features": ["多模态支持", "工作流集成"]
            }
        }
    },
    "timestamp": 1640996400,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "platforms": ["至少需要选择2个平台进行对比"],
            "business_type": ["业务类型不存在"],
            "comparison_metrics": ["对比指标不能为空"]
        }
    },
    "timestamp": 1640996400,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996400,
    "request_id": "req_abc123_def456"
}
```

#### 步骤7: 2.7 按业务类型获取可选平台 GET /api/ai-models/business-platforms

**请求参数示例：**
```json
{
    "business_type": "image_generation",
    "include_metrics": true,
    "include_pricing": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "business_type": "image_generation",
        "business_type_name": "图像生成",
        "available_platforms": [
            {
                "platform": "LiblibAI",
                "platform_name": "LiblibAI",
                "models_count": 1,
                "status": "available",
                "specialties": ["专业图像生成", "ComfyUI工作流", "风格转换"],
                "pricing_range": {
                    "min_credits": 10,
                    "max_credits": 10,
                    "average_credits": 10
                },
                "performance_metrics": {
                    "average_success_rate": 0.99,
                    "average_generation_time": 22.3,
                    "user_satisfaction": 4.9
                },
                "supported_features": [
                    "high_quality",
                    "comfyui_workflow",
                    "style_transfer",
                    "professional_generation"
                ],
                "recommendation_score": 95
            },
            {
                "platform": "KlingAI",
                "platform_name": "KlingAI",
                "models_count": 1,
                "status": "available",
                "specialties": ["高质量图像生成", "图像放大", "图像修复"],
                "pricing_range": {
                    "min_credits": 15,
                    "max_credits": 15,
                    "average_credits": 15
                },
                "performance_metrics": {
                    "average_success_rate": 0.97,
                    "average_generation_time": 18.5,
                    "user_satisfaction": 4.7
                },
                "supported_features": [
                    "high_quality",
                    "image_upscale",
                    "image_repair",
                    "fast_generation"
                ],
                "recommendation_score": 88
            },
            {
                "platform": "MiniMax",
                "platform_name": "MiniMax",
                "models_count": 1,
                "status": "available",
                "specialties": ["多模态图像生成", "图像理解", "上下文感知"],
                "pricing_range": {
                    "min_credits": 12,
                    "max_credits": 12,
                    "average_credits": 12
                },
                "performance_metrics": {
                    "average_success_rate": 0.98,
                    "average_generation_time": 25.5,
                    "user_satisfaction": 4.8
                },
                "supported_features": [
                    "multimodal_generation",
                    "image_understanding",
                    "context_aware",
                    "intelligent_processing"
                ],
                "recommendation_score": 92
            }
        ],
        "platform_comparison": {
            "best_performance": "KlingAI",
            "best_value": "LiblibAI",
            "most_features": "MiniMax",
            "highest_satisfaction": "LiblibAI"
        },
        "selection_guide": {
            "for_beginners": "LiblibAI",
            "for_professionals": "LiblibAI",
            "for_speed": "KlingAI",
            "for_versatility": "MiniMax"
        },
        "total_platforms": 3,
        "total_models": 3
    },
    "timestamp": 1640996460,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 业务类型不存在)：**
```json
{
    "code": 422,
    "message": "业务类型不存在",
    "data": {
        "business_type": "invalid_type",
        "available_types": [
            "image_generation",
            "video_generation",
            "story_generation",
            "character_generation",
            "style_generation",
            "sound_generation",
            "voice_generation",
            "music_generation"
        ]
    },
    "timestamp": 1640996460,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996460,
    "request_id": "req_abc123_def456"
}
```

---

## 🎨 **4.2 风格：通过API测试添加测试数据** (4个接口)

### 📋 **测试目标**
通过API接口测试风格数据的获取和创建功能，为后续业务提供丰富的风格选择。

### 🎭 **风格数据管理**

#### 步骤1: 10.1 获取剧情风格列表 GET /api/styles/list

**请求参数示例：**
```json
{
    "category": "story",
    "page": 1,
    "per_page": 20,
    "sort_by": "popularity",
    "tags": ["现代", "都市"]
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "styles": [
            {
                "style_id": "style_001",
                "style_name": "现代都市风",
                "category": "story",
                "description": "以现代都市为背景的故事风格，注重现实感和都市生活的描绘",
                "tags": ["现代", "都市", "现实主义", "生活"],
                "popularity_score": 95,
                "usage_count": 1250,
                "created_by": "system",
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-01 12:00:00",
                "preview_image": "https://example.com/styles/modern_city.jpg",
                "characteristics": [
                    "现代都市背景",
                    "现实主义风格",
                    "都市生活元素",
                    "现代科技融入"
                ],
                "suitable_genres": ["都市言情", "职场小说", "现代生活"],
                "color_palette": ["#2C3E50", "#34495E", "#95A5A6", "#BDC3C7"],
                "is_premium": false,
                "is_favorite": true
            },
            {
                "style_id": "style_002",
                "style_name": "古风仙侠",
                "category": "story",
                "description": "传统古风与仙侠元素结合，营造飘逸神秘的东方幻想氛围",
                "tags": ["古风", "仙侠", "东方", "幻想"],
                "popularity_score": 88,
                "usage_count": 980,
                "created_by": "system",
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-01 12:00:00",
                "preview_image": "https://example.com/styles/ancient_fantasy.jpg",
                "characteristics": [
                    "古代背景设定",
                    "仙侠修炼体系",
                    "东方神话元素",
                    "飘逸意境描写"
                ],
                "suitable_genres": ["仙侠小说", "古风言情", "东方幻想"],
                "color_palette": ["#8B4513", "#CD853F", "#F4A460", "#FFF8DC"],
                "is_premium": true,
                "is_favorite": false
            },
            {
                "style_id": "style_003",
                "style_name": "科幻未来",
                "category": "story",
                "description": "未来科技与人文思考相结合，探索科技发展对人类社会的影响",
                "tags": ["科幻", "未来", "科技", "思辨"],
                "popularity_score": 82,
                "usage_count": 750,
                "created_by": "system",
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-01 12:00:00",
                "preview_image": "https://example.com/styles/sci_fi_future.jpg",
                "characteristics": [
                    "未来世界设定",
                    "先进科技元素",
                    "人文思辨内容",
                    "社会变革描写"
                ],
                "suitable_genres": ["科幻小说", "未来幻想", "科技惊悚"],
                "color_palette": ["#0F3460", "#16537E", "#1E88E5", "#64B5F6"],
                "is_premium": false,
                "is_favorite": false
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 45,
            "last_page": 3
        },
        "categories": [
            {"value": "story", "label": "剧情风格", "count": 45},
            {"value": "visual", "label": "视觉风格", "count": 38},
            {"value": "audio", "label": "音频风格", "count": 22}
        ],
        "popular_tags": [
            {"tag": "现代", "count": 15},
            {"tag": "古风", "count": 12},
            {"tag": "科幻", "count": 10},
            {"tag": "浪漫", "count": 8}
        ]
    },
    "timestamp": 1640996520,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "category": ["风格分类不存在"],
            "page": ["页码必须大于0"],
            "per_page": ["每页数量必须在1-50之间"]
        }
    },
    "timestamp": 1640996520,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996520,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 10.2 获取风格详情 GET /api/styles/{id}

**请求参数示例：**
```json
{
    "id": "style_001",
    "include_examples": true,
    "include_usage_stats": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "style_id": "style_001",
        "style_name": "现代都市风",
        "category": "story",
        "description": "以现代都市为背景的故事风格，注重现实感和都市生活的描绘",
        "detailed_description": "现代都市风格专注于描绘当代城市生活的方方面面，从繁忙的商业区到宁静的住宅区，从高楼大厦到街头巷尾。这种风格强调现实主义的表现手法，注重细节的真实性和情感的共鸣。",
        "tags": ["现代", "都市", "现实主义", "生活"],
        "popularity_score": 95,
        "usage_count": 1250,
        "created_by": "system",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 12:00:00",
        "preview_image": "https://example.com/styles/modern_city.jpg",
        "characteristics": [
            {
                "name": "现代都市背景",
                "description": "以当代城市为主要场景设定",
                "importance": "high"
            },
            {
                "name": "现实主义风格",
                "description": "注重真实性和可信度",
                "importance": "high"
            },
            {
                "name": "都市生活元素",
                "description": "包含丰富的城市生活细节",
                "importance": "medium"
            },
            {
                "name": "现代科技融入",
                "description": "自然融入现代科技元素",
                "importance": "medium"
            }
        ],
        "suitable_genres": [
            {"genre": "都市言情", "match_score": 0.95},
            {"genre": "职场小说", "match_score": 0.90},
            {"genre": "现代生活", "match_score": 0.88}
        ],
        "color_palette": [
            {"color": "#2C3E50", "name": "深蓝灰", "usage": "主色调"},
            {"color": "#34495E", "name": "石墨蓝", "usage": "辅助色"},
            {"color": "#95A5A6", "name": "银灰", "usage": "点缀色"},
            {"color": "#BDC3C7", "name": "浅灰", "usage": "背景色"}
        ],
        "usage_examples": [
            {
                "title": "都市白领的一天",
                "content": "清晨的阳光透过落地窗洒在办公桌上，李小雅端着咖啡走向会议室...",
                "genre": "都市言情",
                "effectiveness_score": 4.8
            },
            {
                "title": "地铁站的邂逅",
                "content": "下班高峰期的地铁站人潮涌动，在这个钢筋水泥的城市里...",
                "genre": "现代生活",
                "effectiveness_score": 4.6
            }
        ],
        "usage_statistics": {
            "total_projects": 156,
            "success_rate": 0.92,
            "average_rating": 4.7,
            "monthly_growth": 0.15,
            "top_users": [
                {"user_id": "user_001", "usage_count": 25},
                {"user_id": "user_002", "usage_count": 18}
            ]
        },
        "related_styles": [
            {"style_id": "style_004", "style_name": "商务精英", "similarity": 0.85},
            {"style_id": "style_005", "style_name": "都市夜景", "similarity": 0.78}
        ],
        "is_premium": false,
        "is_favorite": true,
        "user_rating": 5,
        "user_notes": "非常适合现代都市题材的创作"
    },
    "timestamp": 1640996580,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 风格不存在)：**
```json
{
    "code": 404,
    "message": "风格不存在",
    "data": {
        "style_id": "invalid_style_id",
        "available_styles": ["style_001", "style_002", "style_003"],
        "suggestion": "请检查风格ID是否正确"
    },
    "timestamp": 1640996580,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996580,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 10.3 获取热门风格 GET /api/styles/popular

**请求参数示例：**
```json
{
    "category": "story",
    "time_range": "last_30_days",
    "limit": 10,
    "include_trending": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "popular_styles": [
            {
                "style_id": "style_001",
                "style_name": "现代都市风",
                "category": "story",
                "popularity_score": 95,
                "usage_count": 1250,
                "growth_rate": 0.25,
                "rank": 1,
                "rank_change": 0,
                "preview_image": "https://example.com/styles/modern_city.jpg",
                "tags": ["现代", "都市", "现实主义", "生活"],
                "description": "以现代都市为背景的故事风格，注重现实感和都市生活的描绘",
                "user_rating": 4.8,
                "is_trending": true,
                "trending_reason": "最近30天使用量增长25%"
            },
            {
                "style_id": "style_002",
                "style_name": "古风仙侠",
                "category": "story",
                "popularity_score": 88,
                "usage_count": 980,
                "growth_rate": 0.18,
                "rank": 2,
                "rank_change": 1,
                "preview_image": "https://example.com/styles/ancient_fantasy.jpg",
                "tags": ["古风", "仙侠", "东方", "幻想"],
                "description": "传统古风与仙侠元素结合，营造飘逸神秘的东方幻想氛围",
                "user_rating": 4.6,
                "is_trending": true,
                "trending_reason": "新增用户喜爱度上升"
            },
            {
                "style_id": "style_003",
                "style_name": "科幻未来",
                "category": "story",
                "popularity_score": 82,
                "usage_count": 750,
                "growth_rate": 0.12,
                "rank": 3,
                "rank_change": -1,
                "preview_image": "https://example.com/styles/sci_fi_future.jpg",
                "tags": ["科幻", "未来", "科技", "思辨"],
                "description": "未来科技与人文思考相结合，探索科技发展对人类社会的影响",
                "user_rating": 4.5,
                "is_trending": false,
                "trending_reason": null
            }
        ],
        "trending_analysis": {
            "total_trending_styles": 2,
            "average_growth_rate": 0.18,
            "top_growth_category": "story",
            "emerging_tags": ["现代", "都市", "古风"],
            "declining_tags": ["传统", "经典"]
        },
        "time_period": {
            "start_date": "2023-12-02",
            "end_date": "2024-01-01",
            "days": 30
        },
        "category_stats": {
            "story": {"total_styles": 45, "trending_count": 2},
            "visual": {"total_styles": 38, "trending_count": 1},
            "audio": {"total_styles": 22, "trending_count": 0}
        },
        "recommendations": [
            {
                "style_id": "style_006",
                "style_name": "赛博朋克",
                "reason": "基于您的浏览历史推荐",
                "match_score": 0.85
            }
        ]
    },
    "timestamp": 1640996640,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "time_range": ["时间范围无效，支持：last_7_days, last_30_days, last_90_days"],
            "limit": ["限制数量必须在1-50之间"],
            "category": ["风格分类不存在"]
        }
    },
    "timestamp": 1640996640,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996640,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 10.4 创建风格 POST /api/styles/create

**请求参数示例：**
```json
{
    "style_name": "温馨家庭风",
    "category": "story",
    "description": "以家庭温暖为核心的故事风格，强调亲情和家庭和谐",
    "detailed_description": "温馨家庭风格专注于描绘家庭生活的美好瞬间，从日常的柴米油盐到节日的团聚欢乐，从代际间的理解沟通到家庭成员间的相互支持。这种风格强调情感的温暖和家庭价值的传承。",
    "tags": ["家庭", "温馨", "亲情", "和谐"],
    "characteristics": [
        "家庭生活场景",
        "温暖情感表达",
        "亲情关系描写",
        "生活化细节"
    ],
    "suitable_genres": ["家庭伦理", "温情小说", "生活剧"],
    "color_palette": ["#F39C12", "#E67E22", "#D35400", "#FFF8DC"],
    "preview_image": "https://example.com/styles/warm_family.jpg",
    "is_public": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "风格创建成功",
    "data": {
        "style_id": "style_new_001",
        "style_name": "温馨家庭风",
        "category": "story",
        "description": "以家庭温暖为核心的故事风格，强调亲情和家庭和谐",
        "detailed_description": "温馨家庭风格专注于描绘家庭生活的美好瞬间，从日常的柴米油盐到节日的团聚欢乐，从代际间的理解沟通到家庭成员间的相互支持。这种风格强调情感的温暖和家庭价值的传承。",
        "tags": ["家庭", "温馨", "亲情", "和谐"],
        "popularity_score": 0,
        "usage_count": 0,
        "created_by": "user_12345",
        "created_at": "2024-01-01 12:30:00",
        "updated_at": "2024-01-01 12:30:00",
        "preview_image": "https://example.com/styles/warm_family.jpg",
        "characteristics": [
            {
                "name": "家庭生活场景",
                "description": "以家庭日常生活为主要背景",
                "importance": "high"
            },
            {
                "name": "温暖情感表达",
                "description": "注重情感的温暖传递",
                "importance": "high"
            },
            {
                "name": "亲情关系描写",
                "description": "深入刻画家庭成员关系",
                "importance": "medium"
            },
            {
                "name": "生活化细节",
                "description": "丰富的生活化场景细节",
                "importance": "medium"
            }
        ],
        "suitable_genres": [
            {"genre": "家庭伦理", "match_score": 0.95},
            {"genre": "温情小说", "match_score": 0.90},
            {"genre": "生活剧", "match_score": 0.85}
        ],
        "color_palette": [
            {"color": "#F39C12", "name": "温暖橙", "usage": "主色调"},
            {"color": "#E67E22", "name": "深橙", "usage": "辅助色"},
            {"color": "#D35400", "name": "暖红橙", "usage": "点缀色"},
            {"color": "#FFF8DC", "name": "米白", "usage": "背景色"}
        ],
        "status": "pending_review",
        "is_public": true,
        "is_premium": false,
        "review_notes": "风格已创建，等待管理员审核后公开",
        "estimated_review_time": "1-3个工作日"
    },
    "timestamp": 1640996700,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "style_name": ["风格名称不能为空", "风格名称长度不能超过50字符"],
            "category": ["风格分类不存在"],
            "description": ["描述不能为空", "描述长度不能超过200字符"],
            "tags": ["标签不能为空", "标签数量不能超过10个"]
        }
    },
    "timestamp": 1640996700,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1005 - 风格名称已存在)：**
```json
{
    "code": 1005,
    "message": "风格名称已存在",
    "data": {
        "style_name": "温馨家庭风",
        "existing_style_id": "style_005",
        "suggestion": "请使用不同的风格名称或在现有风格基础上修改"
    },
    "timestamp": 1640996700,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996700,
    "request_id": "req_abc123_def456"
}
```

---

## 👤 **4.3 角色：通过API测试添加测试数据** (9个接口)

### 📋 **测试目标**
通过API接口测试角色数据的获取、绑定和生成功能，为后续业务提供丰富的角色选择。

### 🎭 **角色数据管理**

#### 步骤1: 18.1 角色分类列表 GET /api/characters/categories

**请求参数示例：**
```json
{
    "include_count": true,
    "include_popular": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "categories": [
            {
                "category_id": "cat_001",
                "category_name": "现代职场",
                "description": "现代都市职场环境中的各类角色",
                "icon": "https://example.com/icons/modern_workplace.png",
                "character_count": 45,
                "popular_characters": [
                    {"character_id": "char_001", "character_name": "都市白领", "usage_count": 320},
                    {"character_id": "char_002", "character_name": "创业CEO", "usage_count": 280}
                ],
                "tags": ["现代", "职场", "都市", "商务"],
                "created_at": "2024-01-01 10:00:00",
                "is_active": true
            },
            {
                "category_id": "cat_002",
                "category_name": "古风仙侠",
                "description": "古代背景和仙侠世界中的经典角色",
                "icon": "https://example.com/icons/ancient_fantasy.png",
                "character_count": 38,
                "popular_characters": [
                    {"character_id": "char_003", "character_name": "剑仙", "usage_count": 450},
                    {"character_id": "char_004", "character_name": "古风美人", "usage_count": 380}
                ],
                "tags": ["古风", "仙侠", "武侠", "古代"],
                "created_at": "2024-01-01 10:00:00",
                "is_active": true
            },
            {
                "category_id": "cat_003",
                "category_name": "科幻未来",
                "description": "未来世界和科幻背景中的角色设定",
                "icon": "https://example.com/icons/sci_fi.png",
                "character_count": 32,
                "popular_characters": [
                    {"character_id": "char_005", "character_name": "机械工程师", "usage_count": 220},
                    {"character_id": "char_006", "character_name": "星际探险家", "usage_count": 195}
                ],
                "tags": ["科幻", "未来", "科技", "太空"],
                "created_at": "2024-01-01 10:00:00",
                "is_active": true
            },
            {
                "category_id": "cat_004",
                "category_name": "校园青春",
                "description": "学校环境中的青春角色类型",
                "icon": "https://example.com/icons/campus.png",
                "character_count": 28,
                "popular_characters": [
                    {"character_id": "char_007", "character_name": "学霸同桌", "usage_count": 340},
                    {"character_id": "char_008", "character_name": "青春少女", "usage_count": 310}
                ],
                "tags": ["校园", "青春", "学生", "成长"],
                "created_at": "2024-01-01 10:00:00",
                "is_active": true
            },
            {
                "category_id": "cat_005",
                "category_name": "奇幻魔法",
                "description": "魔法世界和奇幻背景中的角色",
                "icon": "https://example.com/icons/fantasy_magic.png",
                "character_count": 35,
                "popular_characters": [
                    {"character_id": "char_009", "character_name": "魔法师", "usage_count": 290},
                    {"character_id": "char_010", "character_name": "精灵公主", "usage_count": 265}
                ],
                "tags": ["奇幻", "魔法", "精灵", "魔法师"],
                "created_at": "2024-01-01 10:00:00",
                "is_active": true
            }
        ],
        "statistics": {
            "total_categories": 5,
            "total_characters": 178,
            "most_popular_category": "古风仙侠",
            "newest_category": "科幻未来",
            "average_characters_per_category": 35.6
        },
        "popular_tags": [
            {"tag": "现代", "count": 45},
            {"tag": "古风", "count": 38},
            {"tag": "科幻", "count": 32},
            {"tag": "校园", "count": 28},
            {"tag": "奇幻", "count": 35}
        ]
    },
    "timestamp": 1640996760,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996760,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 18.2 角色列表 GET /api/characters/list

**请求参数示例：**
```json
{
    "category_id": "cat_001",
    "page": 1,
    "per_page": 20,
    "sort_by": "popularity",
    "tags": ["现代", "职场"],
    "gender": "female",
    "age_range": "25-35"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "characters": [
            {
                "character_id": "char_001",
                "character_name": "都市白领",
                "category": "现代职场",
                "description": "现代都市中的职场精英，具备专业素养和都市气质",
                "avatar": "https://example.com/avatars/urban_professional.jpg",
                "tags": ["现代", "职场", "都市", "专业"],
                "attributes": {
                    "gender": "female",
                    "age": "28",
                    "personality": ["理性", "独立", "进取", "优雅"],
                    "occupation": "市场总监",
                    "background": "名校毕业，在知名企业工作5年",
                    "skills": ["市场分析", "团队管理", "商务谈判", "数据分析"]
                },
                "appearance": {
                    "height": "165cm",
                    "build": "苗条",
                    "hair": "黑色长发",
                    "style": "职业装",
                    "features": ["明亮的眼睛", "自信的笑容", "优雅的气质"]
                },
                "voice_characteristics": {
                    "tone": "清晰专业",
                    "pace": "适中",
                    "accent": "标准普通话",
                    "emotion_range": ["自信", "温和", "严肃", "友善"]
                },
                "usage_count": 320,
                "popularity_score": 95,
                "user_rating": 4.8,
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-01 12:00:00",
                "is_premium": false,
                "is_favorite": true
            },
            {
                "character_id": "char_002",
                "character_name": "创业CEO",
                "category": "现代职场",
                "description": "年轻有为的创业公司CEO，充满激情和创新精神",
                "avatar": "https://example.com/avatars/startup_ceo.jpg",
                "tags": ["现代", "创业", "领导", "创新"],
                "attributes": {
                    "gender": "male",
                    "age": "32",
                    "personality": ["果断", "创新", "激情", "坚韧"],
                    "occupation": "科技公司CEO",
                    "background": "连续创业者，有过成功和失败的经历",
                    "skills": ["战略规划", "融资谈判", "产品设计", "团队激励"]
                },
                "appearance": {
                    "height": "178cm",
                    "build": "健壮",
                    "hair": "短发",
                    "style": "商务休闲",
                    "features": ["锐利的眼神", "坚定的表情", "自信的姿态"]
                },
                "voice_characteristics": {
                    "tone": "有力坚定",
                    "pace": "快速",
                    "accent": "标准普通话",
                    "emotion_range": ["激情", "坚定", "鼓舞", "专注"]
                },
                "usage_count": 280,
                "popularity_score": 88,
                "user_rating": 4.6,
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-01 12:00:00",
                "is_premium": true,
                "is_favorite": false
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 45,
            "last_page": 3
        },
        "filters": {
            "available_categories": [
                {"id": "cat_001", "name": "现代职场", "count": 45},
                {"id": "cat_002", "name": "古风仙侠", "count": 38}
            ],
            "available_tags": ["现代", "职场", "都市", "专业", "创业"],
            "gender_options": ["male", "female", "other"],
            "age_ranges": ["18-25", "25-35", "35-45", "45+"]
        },
        "sort_options": [
            {"value": "popularity", "label": "热度排序"},
            {"value": "created_at", "label": "创建时间"},
            {"value": "usage_count", "label": "使用次数"},
            {"value": "rating", "label": "用户评分"}
        ]
    },
    "timestamp": 1640996820,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "category_id": ["角色分类不存在"],
            "page": ["页码必须大于0"],
            "per_page": ["每页数量必须在1-50之间"],
            "gender": ["性别选项无效"],
            "age_range": ["年龄范围格式错误"]
        }
    },
    "timestamp": 1640996820,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996820,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 18.3 获取角色详情 GET /api/characters/{id}

**请求参数示例：**
```json
{
    "id": "char_001",
    "include_usage_stats": true,
    "include_related": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "character_id": "char_001",
        "character_name": "都市白领",
        "category": "现代职场",
        "description": "现代都市中的职场精英，具备专业素养和都市气质",
        "detailed_description": "李雅婷，28岁，毕业于知名商学院，现任某知名企业市场总监。她拥有敏锐的市场洞察力和出色的团队管理能力，在职场中展现出独立自信的现代女性魅力。工作之余，她热爱阅读和健身，追求工作与生活的平衡。",
        "avatar": "https://example.com/avatars/urban_professional.jpg",
        "gallery": [
            "https://example.com/gallery/char_001_1.jpg",
            "https://example.com/gallery/char_001_2.jpg",
            "https://example.com/gallery/char_001_3.jpg"
        ],
        "tags": ["现代", "职场", "都市", "专业"],
        "attributes": {
            "basic_info": {
                "gender": "female",
                "age": "28",
                "height": "165cm",
                "weight": "52kg",
                "blood_type": "A型",
                "zodiac": "天秤座"
            },
            "personality": {
                "core_traits": ["理性", "独立", "进取", "优雅"],
                "mbti": "ENTJ",
                "strengths": ["逻辑思维强", "执行力高", "沟通能力佳"],
                "weaknesses": ["有时过于追求完美", "工作压力大时容易焦虑"],
                "values": ["成就感", "公平正义", "个人成长", "团队合作"]
            },
            "professional": {
                "occupation": "市场总监",
                "company": "某知名科技企业",
                "experience": "5年市场营销经验",
                "education": "知名商学院MBA",
                "skills": ["市场分析", "团队管理", "商务谈判", "数据分析"],
                "achievements": ["年度最佳员工", "成功推出3个爆款产品", "团队业绩增长200%"]
            },
            "lifestyle": {
                "hobbies": ["阅读", "健身", "旅行", "品酒"],
                "living_style": "简约现代",
                "social_circle": "职场精英、创业者、文艺青年",
                "daily_routine": "早起健身、高效工作、晚上充电学习"
            }
        },
        "appearance": {
            "physical": {
                "height": "165cm",
                "build": "苗条",
                "hair": "黑色长发，通常扎成低马尾",
                "eyes": "深邃的黑色眼睛，眼神坚定",
                "skin": "白皙光滑",
                "features": ["高挺的鼻梁", "优雅的颈线", "自信的笑容"]
            },
            "style": {
                "work_attire": "经典职业套装，颜色以黑、白、灰为主",
                "casual_wear": "简约时尚的休闲装",
                "accessories": ["精致的手表", "简约的项链", "高质量的包包"],
                "makeup": "精致但不浓重的职业妆容"
            }
        },
        "voice_characteristics": {
            "basic": {
                "tone": "清晰专业",
                "pitch": "中音",
                "pace": "适中，重要内容会放慢",
                "volume": "适中，会议时会提高音量",
                "accent": "标准普通话，偶有轻微北方口音"
            },
            "emotional_range": [
                {"emotion": "自信", "description": "声音坚定有力，语调上扬"},
                {"emotion": "温和", "description": "语调柔和，语速稍慢"},
                {"emotion": "严肃", "description": "语调低沉，字字清晰"},
                {"emotion": "友善", "description": "语调轻快，带有笑意"}
            ],
            "speaking_habits": [
                "喜欢用数据支撑观点",
                "会用手势辅助表达",
                "重要内容会重复强调",
                "善于用比喻解释复杂概念"
            ]
        },
        "usage_statistics": {
            "total_usage": 320,
            "monthly_usage": 45,
            "success_rate": 0.94,
            "user_satisfaction": 4.8,
            "popular_scenarios": [
                {"scenario": "商务谈判", "usage_count": 85},
                {"scenario": "团队会议", "usage_count": 72},
                {"scenario": "客户沟通", "usage_count": 68}
            ],
            "user_feedback": [
                {"rating": 5, "comment": "角色设定很真实，很有代入感"},
                {"rating": 5, "comment": "声音特征描述很详细，便于配音"}
            ]
        },
        "related_characters": [
            {
                "character_id": "char_002",
                "character_name": "创业CEO",
                "similarity": 0.78,
                "relation_type": "同类型"
            },
            {
                "character_id": "char_011",
                "character_name": "商务助理",
                "similarity": 0.65,
                "relation_type": "职场搭档"
            }
        ],
        "created_by": "system",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 12:00:00",
        "is_premium": false,
        "is_favorite": true,
        "user_rating": 5,
        "user_notes": "非常适合现代职场剧的女主角设定"
    },
    "timestamp": 1640996880,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 角色不存在)：**
```json
{
    "code": 404,
    "message": "角色不存在",
    "data": {
        "character_id": "invalid_char_id",
        "available_characters": ["char_001", "char_002", "char_003"],
        "suggestion": "请检查角色ID是否正确"
    },
    "timestamp": 1640996880,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996880,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 18.4 推荐角色 GET /api/characters/recommendations

**请求参数示例：**
```json
{
    "based_on": "usage_history",
    "scenario": "商务谈判",
    "genre": "都市言情",
    "limit": 5,
    "exclude_owned": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "recommendations": [
            {
                "character_id": "char_012",
                "character_name": "商务精英",
                "category": "现代职场",
                "description": "资深商务人士，擅长高端商务谈判和战略规划",
                "avatar": "https://example.com/avatars/business_elite.jpg",
                "match_score": 0.95,
                "recommendation_reasons": [
                    "与您常用的角色类型高度匹配",
                    "在商务谈判场景中表现优秀",
                    "用户评分高达4.9分"
                ],
                "tags": ["商务", "谈判", "精英", "专业"],
                "attributes": {
                    "gender": "male",
                    "age": "35",
                    "personality": ["沉稳", "睿智", "果断", "魅力"],
                    "occupation": "投资总监"
                },
                "popularity_score": 92,
                "user_rating": 4.9,
                "usage_count": 450,
                "is_premium": true,
                "estimated_fit": "excellent"
            },
            {
                "character_id": "char_013",
                "character_name": "都市女强人",
                "category": "现代职场",
                "description": "独立自强的现代女性，在商界叱咤风云",
                "avatar": "https://example.com/avatars/strong_woman.jpg",
                "match_score": 0.88,
                "recommendation_reasons": [
                    "与您的使用偏好相符",
                    "适合都市言情题材",
                    "角色设定丰富完整"
                ],
                "tags": ["都市", "女强人", "独立", "成功"],
                "attributes": {
                    "gender": "female",
                    "age": "32",
                    "personality": ["坚强", "独立", "聪慧", "优雅"],
                    "occupation": "公司CEO"
                },
                "popularity_score": 85,
                "user_rating": 4.7,
                "usage_count": 380,
                "is_premium": false,
                "estimated_fit": "very_good"
            },
            {
                "character_id": "char_014",
                "character_name": "金融分析师",
                "category": "现代职场",
                "description": "专业的金融分析师，理性冷静，数据驱动",
                "avatar": "https://example.com/avatars/financial_analyst.jpg",
                "match_score": 0.82,
                "recommendation_reasons": [
                    "专业背景与商务场景匹配",
                    "理性角色类型符合您的偏好",
                    "在同类角色中评价较高"
                ],
                "tags": ["金融", "分析", "理性", "专业"],
                "attributes": {
                    "gender": "female",
                    "age": "29",
                    "personality": ["理性", "严谨", "专注", "冷静"],
                    "occupation": "高级分析师"
                },
                "popularity_score": 78,
                "user_rating": 4.5,
                "usage_count": 290,
                "is_premium": false,
                "estimated_fit": "good"
            }
        ],
        "recommendation_basis": {
            "user_preferences": {
                "favorite_categories": ["现代职场", "都市生活"],
                "preferred_gender": "female",
                "preferred_age_range": "25-35",
                "common_scenarios": ["商务谈判", "职场对话", "都市生活"]
            },
            "usage_patterns": {
                "most_used_characters": ["char_001", "char_002"],
                "success_scenarios": ["商务谈判", "团队会议"],
                "preferred_personality_traits": ["理性", "独立", "专业"]
            }
        },
        "alternative_suggestions": [
            {
                "suggestion_type": "cross_category",
                "message": "您可能也会喜欢古风仙侠类的女性角色",
                "characters": ["char_003", "char_004"]
            },
            {
                "suggestion_type": "trending",
                "message": "最近热门的新角色推荐",
                "characters": ["char_015", "char_016"]
            }
        ],
        "personalization_score": 0.87,
        "total_available": 156,
        "filtered_count": 3
    },
    "timestamp": 1640996940,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "based_on": ["推荐依据无效，支持：usage_history, preferences, similarity"],
            "limit": ["推荐数量必须在1-20之间"],
            "scenario": ["场景类型不存在"],
            "genre": ["题材类型不支持"]
        }
    },
    "timestamp": 1640996940,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640996940,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 18.5 角色绑定 POST /api/characters/bind

**请求参数示例：**
```json
{
    "character_id": "char_001",
    "binding_name": "我的职场女主",
    "project_id": "project_123",
    "customizations": {
        "name_override": "林雅婷",
        "personality_adjustments": {
            "add_traits": ["幽默感"],
            "remove_traits": ["过于严肃"],
            "emphasis": ["独立", "专业"]
        },
        "appearance_modifications": {
            "hair_color": "棕色",
            "style_preference": "更加时尚的职业装"
        },
        "voice_adjustments": {
            "tone_preference": "更加温和",
            "emotion_emphasis": ["友善", "自信"]
        }
    },
    "usage_scenarios": ["商务谈判", "团队会议", "客户沟通"],
    "notes": "用于都市职场剧的女主角，需要体现现代女性的独立和专业"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "角色绑定成功",
    "data": {
        "binding_id": "binding_001",
        "character_id": "char_001",
        "original_character_name": "都市白领",
        "binding_name": "我的职场女主",
        "project_id": "project_123",
        "project_name": "都市职场剧",
        "customizations": {
            "name_override": "林雅婷",
            "personality_adjustments": {
                "original_traits": ["理性", "独立", "进取", "优雅"],
                "modified_traits": ["理性", "独立", "进取", "优雅", "幽默感"],
                "removed_traits": ["过于严肃"],
                "emphasis": ["独立", "专业"]
            },
            "appearance_modifications": {
                "original_hair": "黑色长发",
                "modified_hair": "棕色长发",
                "style_preference": "更加时尚的职业装",
                "modification_preview": "https://example.com/previews/binding_001.jpg"
            },
            "voice_adjustments": {
                "original_tone": "清晰专业",
                "modified_tone": "清晰专业且温和",
                "emotion_emphasis": ["友善", "自信"],
                "voice_sample": "https://example.com/voice_samples/binding_001.mp3"
            }
        },
        "usage_scenarios": ["商务谈判", "团队会议", "客户沟通"],
        "binding_status": "active",
        "compatibility_score": 0.95,
        "estimated_effectiveness": "excellent",
        "usage_statistics": {
            "total_usage": 0,
            "success_rate": null,
            "user_satisfaction": null
        },
        "created_at": "2024-01-01 12:45:00",
        "updated_at": "2024-01-01 12:45:00",
        "expires_at": null,
        "notes": "用于都市职场剧的女主角，需要体现现代女性的独立和专业",
        "sharing_settings": {
            "is_public": false,
            "allow_copy": false,
            "visibility": "private"
        },
        "quick_actions": [
            {
                "action": "test_voice",
                "label": "测试语音效果",
                "url": "/api/characters/bindings/binding_001/test-voice"
            },
            {
                "action": "preview_appearance",
                "label": "预览外观效果",
                "url": "/api/characters/bindings/binding_001/preview"
            },
            {
                "action": "generate_sample",
                "label": "生成示例对话",
                "url": "/api/characters/bindings/binding_001/generate-sample"
            }
        ]
    },
    "timestamp": 1640997000,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 角色不存在)：**
```json
{
    "code": 404,
    "message": "角色不存在",
    "data": {
        "character_id": "invalid_char_id",
        "available_characters": ["char_001", "char_002", "char_003"]
    },
    "timestamp": 1640997000,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1006 - 绑定名称已存在)：**
```json
{
    "code": 1006,
    "message": "绑定名称已存在",
    "data": {
        "binding_name": "我的职场女主",
        "existing_binding_id": "binding_002",
        "suggestion": "请使用不同的绑定名称"
    },
    "timestamp": 1640997000,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997000,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 18.6 获取我的角色绑定 GET /api/characters/my-bindings

**请求参数示例：**
```json
{
    "project_id": "project_123",
    "status": "active",
    "page": 1,
    "per_page": 10,
    "sort_by": "created_at"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "bindings": [
            {
                "binding_id": "binding_001",
                "character_id": "char_001",
                "original_character_name": "都市白领",
                "binding_name": "我的职场女主",
                "project_id": "project_123",
                "project_name": "都市职场剧",
                "customizations_summary": {
                    "name_override": "林雅婷",
                    "has_personality_changes": true,
                    "has_appearance_changes": true,
                    "has_voice_changes": true
                },
                "usage_scenarios": ["商务谈判", "团队会议", "客户沟通"],
                "binding_status": "active",
                "usage_statistics": {
                    "total_usage": 15,
                    "success_rate": 0.93,
                    "user_satisfaction": 4.7,
                    "last_used": "2024-01-01 11:30:00"
                },
                "created_at": "2024-01-01 12:45:00",
                "updated_at": "2024-01-01 12:45:00",
                "preview_image": "https://example.com/previews/binding_001.jpg"
            },
            {
                "binding_id": "binding_002",
                "character_id": "char_003",
                "original_character_name": "剑仙",
                "binding_name": "我的古风男主",
                "project_id": "project_124",
                "project_name": "仙侠传奇",
                "customizations_summary": {
                    "name_override": "云逸风",
                    "has_personality_changes": false,
                    "has_appearance_changes": true,
                    "has_voice_changes": false
                },
                "usage_scenarios": ["武打场面", "情感对话", "修炼场景"],
                "binding_status": "active",
                "usage_statistics": {
                    "total_usage": 8,
                    "success_rate": 0.88,
                    "user_satisfaction": 4.5,
                    "last_used": "2024-01-01 10:15:00"
                },
                "created_at": "2024-01-01 09:30:00",
                "updated_at": "2024-01-01 10:15:00",
                "preview_image": "https://example.com/previews/binding_002.jpg"
            },
            {
                "binding_id": "binding_003",
                "character_id": "char_007",
                "original_character_name": "学霸同桌",
                "binding_name": "校园女神",
                "project_id": "project_125",
                "project_name": "青春校园",
                "customizations_summary": {
                    "name_override": "苏晴雨",
                    "has_personality_changes": true,
                    "has_appearance_changes": false,
                    "has_voice_changes": true
                },
                "usage_scenarios": ["校园对话", "学习场景", "青春互动"],
                "binding_status": "paused",
                "usage_statistics": {
                    "total_usage": 3,
                    "success_rate": 1.0,
                    "user_satisfaction": 5.0,
                    "last_used": "2023-12-28 16:20:00"
                },
                "created_at": "2023-12-28 15:00:00",
                "updated_at": "2023-12-30 09:00:00",
                "preview_image": "https://example.com/previews/binding_003.jpg"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 10,
            "total": 3,
            "last_page": 1
        },
        "statistics": {
            "total_bindings": 3,
            "active_bindings": 2,
            "paused_bindings": 1,
            "total_usage": 26,
            "average_satisfaction": 4.73,
            "most_used_binding": "binding_001",
            "most_successful_binding": "binding_003"
        },
        "quick_filters": [
            {"filter": "active", "count": 2, "label": "活跃绑定"},
            {"filter": "paused", "count": 1, "label": "暂停绑定"},
            {"filter": "recent", "count": 2, "label": "最近使用"}
        ]
    },
    "timestamp": 1640997060,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "status": ["状态值无效，支持：active, paused, archived"],
            "page": ["页码必须大于0"],
            "per_page": ["每页数量必须在1-50之间"]
        }
    },
    "timestamp": 1640997060,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997060,
    "request_id": "req_abc123_def456"
}
```

#### 步骤7: 18.7 更新角色绑定 PUT /api/characters/bindings/{id}

**请求参数示例：**
```json
{
    "id": "binding_001",
    "binding_name": "我的职场女主角",
    "customizations": {
        "name_override": "林雅婷",
        "personality_adjustments": {
            "add_traits": ["幽默感", "亲和力"],
            "remove_traits": [],
            "emphasis": ["独立", "专业", "温和"]
        },
        "appearance_modifications": {
            "hair_color": "深棕色",
            "style_preference": "时尚职业装配饰"
        },
        "voice_adjustments": {
            "tone_preference": "温和专业",
            "emotion_emphasis": ["友善", "自信", "温暖"]
        }
    },
    "usage_scenarios": ["商务谈判", "团队会议", "客户沟通", "内部培训"],
    "notes": "增加了亲和力特质，适合更多样化的职场场景"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "角色绑定更新成功",
    "data": {
        "binding_id": "binding_001",
        "character_id": "char_001",
        "original_character_name": "都市白领",
        "binding_name": "我的职场女主角",
        "changes_summary": {
            "modified_fields": ["binding_name", "personality_adjustments", "appearance_modifications", "voice_adjustments", "usage_scenarios", "notes"],
            "personality_changes": {
                "added_traits": ["亲和力"],
                "emphasis_changes": ["温和"]
            },
            "appearance_changes": {
                "hair_color": "黑色长发 → 深棕色长发",
                "style_updates": "增加时尚配饰元素"
            },
            "voice_changes": {
                "emotion_additions": ["温暖"]
            },
            "scenario_additions": ["内部培训"]
        },
        "updated_customizations": {
            "name_override": "林雅婷",
            "personality_adjustments": {
                "modified_traits": ["理性", "独立", "进取", "优雅", "幽默感", "亲和力"],
                "emphasis": ["独立", "专业", "温和"]
            },
            "appearance_modifications": {
                "hair_color": "深棕色",
                "style_preference": "时尚职业装配饰",
                "modification_preview": "https://example.com/previews/binding_001_updated.jpg"
            },
            "voice_adjustments": {
                "tone_preference": "温和专业",
                "emotion_emphasis": ["友善", "自信", "温暖"],
                "updated_voice_sample": "https://example.com/voice_samples/binding_001_updated.mp3"
            }
        },
        "usage_scenarios": ["商务谈判", "团队会议", "客户沟通", "内部培训"],
        "compatibility_score": 0.97,
        "estimated_effectiveness": "excellent",
        "updated_at": "2024-01-01 13:15:00",
        "notes": "增加了亲和力特质，适合更多样化的职场场景",
        "version": 2,
        "change_history": [
            {
                "version": 1,
                "changed_at": "2024-01-01 12:45:00",
                "changes": "初始创建"
            },
            {
                "version": 2,
                "changed_at": "2024-01-01 13:15:00",
                "changes": "增加亲和力特质，更新外观和语音设置"
            }
        ]
    },
    "timestamp": 1640997120,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 绑定不存在)：**
```json
{
    "code": 404,
    "message": "角色绑定不存在",
    "data": {
        "binding_id": "invalid_binding_id",
        "user_bindings": ["binding_001", "binding_002", "binding_003"]
    },
    "timestamp": 1640997120,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997120,
    "request_id": "req_abc123_def456"
}
```

#### 步骤8: 18.8 解绑角色 DELETE /api/characters/unbind

**请求参数示例：**
```json
{
    "binding_id": "binding_003",
    "reason": "项目已完成",
    "backup_data": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "角色解绑成功",
    "data": {
        "binding_id": "binding_003",
        "character_id": "char_007",
        "character_name": "学霸同桌",
        "binding_name": "校园女神",
        "project_id": "project_125",
        "unbind_summary": {
            "total_usage": 3,
            "success_rate": 1.0,
            "user_satisfaction": 5.0,
            "active_duration": "3天",
            "last_used": "2023-12-28 16:20:00"
        },
        "backup_info": {
            "backup_created": true,
            "backup_id": "backup_binding_003_20240101",
            "backup_location": "user_backups/binding_003",
            "restore_available_until": "2024-04-01 13:20:00",
            "backup_size": "2.5MB"
        },
        "cleanup_actions": [
            "删除临时文件",
            "清理缓存数据",
            "移除项目关联",
            "归档使用记录"
        ],
        "unbound_at": "2024-01-01 13:20:00",
        "reason": "项目已完成",
        "recovery_options": {
            "can_restore": true,
            "restore_deadline": "2024-04-01 13:20:00",
            "restore_url": "/api/characters/bindings/restore/backup_binding_003_20240101"
        }
    },
    "timestamp": 1640997180,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 绑定不存在)：**
```json
{
    "code": 404,
    "message": "角色绑定不存在",
    "data": {
        "binding_id": "invalid_binding_id",
        "user_bindings": ["binding_001", "binding_002"]
    },
    "timestamp": 1640997180,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997180,
    "request_id": "req_abc123_def456"
}
```

#### 步骤9: 27.1 角色生成 POST /api/characters/generate

**请求参数示例：**
```json
{
    "character_type": "custom",
    "generation_params": {
        "category": "现代职场",
        "gender": "female",
        "age_range": "25-30",
        "personality_keywords": ["聪明", "独立", "温和", "专业"],
        "occupation": "产品经理",
        "background_story": "海归硕士，在互联网公司担任产品经理，负责用户体验设计",
        "appearance_style": "现代简约",
        "voice_style": "清晰专业"
    },
    "customization_level": "detailed",
    "include_voice_sample": true,
    "include_appearance_preview": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "角色生成成功",
    "data": {
        "generated_character": {
            "character_id": "char_generated_001",
            "character_name": "智慧产品经理",
            "category": "现代职场",
            "description": "海归背景的产品经理，具备国际视野和专业素养，擅长用户体验设计和产品规划",
            "detailed_description": "陈思雨，27岁，毕业于美国知名大学用户体验设计专业，回国后在知名互联网公司担任高级产品经理。她具备敏锐的市场洞察力和用户同理心，善于将复杂的技术概念转化为用户友好的产品功能。工作中追求完美，生活中热爱旅行和摄影。",
            "generation_metadata": {
                "generation_time": "2024-01-01 13:25:00",
                "generation_model": "character_ai_v2.1",
                "generation_quality": "high",
                "uniqueness_score": 0.92,
                "coherence_score": 0.95
            },
            "attributes": {
                "basic_info": {
                    "gender": "female",
                    "age": "27",
                    "height": "162cm",
                    "nationality": "中国",
                    "education": "美国知名大学硕士"
                },
                "personality": {
                    "core_traits": ["聪明", "独立", "温和", "专业", "细致", "创新"],
                    "mbti": "INFJ",
                    "strengths": ["用户同理心强", "逻辑思维清晰", "国际视野开阔"],
                    "weaknesses": ["有时过于追求完美", "对细节要求过高"],
                    "values": ["用户体验", "产品价值", "团队协作", "持续学习"]
                },
                "professional": {
                    "occupation": "高级产品经理",
                    "company": "知名互联网公司",
                    "experience": "3年产品经验",
                    "specialties": ["用户体验设计", "产品规划", "数据分析", "跨文化沟通"],
                    "achievements": ["主导设计的产品获得用户好评", "成功推出3个核心功能模块"]
                },
                "lifestyle": {
                    "hobbies": ["旅行", "摄影", "阅读设计书籍", "学习新技术"],
                    "living_style": "简约现代",
                    "social_preferences": "小圈子深度交流",
                    "work_life_balance": "注重效率，追求平衡"
                }
            },
            "appearance": {
                "physical": {
                    "height": "162cm",
                    "build": "匀称",
                    "hair": "中长发，自然黑色，偶尔扎成低马尾",
                    "eyes": "明亮的黑色眼睛，戴时尚眼镜",
                    "style": "简约现代的职业装，偏爱中性色调"
                },
                "generated_preview": {
                    "preview_image": "https://example.com/generated/char_generated_001_preview.jpg",
                    "style_variations": [
                        "https://example.com/generated/char_generated_001_casual.jpg",
                        "https://example.com/generated/char_generated_001_formal.jpg"
                    ]
                }
            },
            "voice_characteristics": {
                "basic": {
                    "tone": "清晰专业，略带温和",
                    "pitch": "中音偏高",
                    "pace": "适中，讲解时会放慢",
                    "accent": "标准普通话，偶有轻微海归口音"
                },
                "emotional_range": [
                    {"emotion": "专业", "description": "语调稳定，逻辑清晰"},
                    {"emotion": "友善", "description": "语调温和，带有笑意"},
                    {"emotion": "专注", "description": "语速稍快，语调坚定"},
                    {"emotion": "解释", "description": "语调耐心，善用比喻"}
                ],
                "generated_voice_sample": {
                    "sample_url": "https://example.com/voice_samples/char_generated_001.mp3",
                    "sample_text": "大家好，我是陈思雨，很高兴和大家分享今天的产品设计思路。",
                    "sample_duration": 8.5
                }
            },
            "usage_suggestions": {
                "best_scenarios": ["产品会议", "用户调研", "设计评审", "团队协作"],
                "suitable_genres": ["职场剧", "创业故事", "现代都市"],
                "personality_highlights": ["专业素养", "国际视野", "用户思维"],
                "voice_applications": ["产品介绍", "用户访谈", "团队沟通"]
            }
        },
        "generation_options": {
            "alternative_versions": [
                {
                    "version_id": "alt_001",
                    "variation_type": "personality_emphasis",
                    "description": "更加强调创新和领导力特质",
                    "preview_url": "https://example.com/alternatives/alt_001_preview.jpg"
                },
                {
                    "version_id": "alt_002",
                    "variation_type": "age_adjustment",
                    "description": "年龄调整为30-32岁，更加成熟稳重",
                    "preview_url": "https://example.com/alternatives/alt_002_preview.jpg"
                }
            ],
            "customization_suggestions": [
                "可以调整发型为短发增加干练感",
                "可以增加'果断'特质适应管理角色",
                "可以调整声音语调更加权威"
            ]
        },
        "save_options": {
            "can_save_to_library": true,
            "suggested_name": "智慧产品经理",
            "estimated_popularity": "high",
            "save_url": "/api/characters/save-generated"
        }
    },
    "timestamp": 1640997240,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "character_type": ["角色类型无效"],
            "generation_params.category": ["角色分类不存在"],
            "generation_params.age_range": ["年龄范围格式错误"],
            "generation_params.personality_keywords": ["个性关键词不能为空"]
        }
    },
    "timestamp": 1640997240,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1014 - 生成失败)：**
```json
{
    "code": 1014,
    "message": "角色生成失败",
    "data": {
        "error_type": "generation_timeout",
        "error_details": "AI模型响应超时",
        "retry_suggestion": "请稍后重试或降低自定义复杂度",
        "estimated_retry_time": 300
    },
    "timestamp": 1640997240,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997240,
    "request_id": "req_abc123_def456"
}
```

---

## 🎵 **4.4 音色：通过API测试添加测试数据** (10个接口)

### 📋 **测试目标**
通过API接口测试音色合成、克隆和自定义功能，验证多平台语音AI服务的稳定性。

### 🔊 **音色合成与管理**

#### 步骤1: 48.1 智能语音合成 POST /api/voices/synthesize

**请求参数示例：**
```json
{
    "text": "欢迎使用我们的智能语音合成服务，这是一段测试音频。",
    "voice_id": "voice_001",
    "platform": "火山引擎豆包",
    "voice_settings": {
        "speed": 1.0,
        "pitch": 0.0,
        "volume": 0.8,
        "emotion": "neutral",
        "style": "professional"
    },
    "output_format": "mp3",
    "sample_rate": 24000,
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "语音合成任务创建成功",
    "data": {
        "task_id": "voice_task_001",
        "text": "欢迎使用我们的智能语音合成服务，这是一段测试音频。",
        "voice_id": "voice_001",
        "voice_name": "专业女声",
        "platform": "火山引擎豆包",
        "platform_model": "volcengine_tts_v1",
        "voice_settings": {
            "speed": 1.0,
            "pitch": 0.0,
            "volume": 0.8,
            "emotion": "neutral",
            "style": "professional"
        },
        "output_format": "mp3",
        "sample_rate": 24000,
        "estimated_duration": 8.5,
        "estimated_completion_time": "2024-01-01 13:30:30",
        "task_status": "processing",
        "created_at": "2024-01-01 13:30:00",
        "project_id": "project_123",
        "credits_cost": 15,
        "quality_level": "high",
        "processing_queue": {
            "position": 2,
            "estimated_wait_time": 30
        },
        "status_check_url": "/api/voices/voice_task_001/status",
        "webhook_url": null
    },
    "timestamp": 1640997300,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "text": ["文本内容不能为空", "文本长度不能超过2000字符"],
            "voice_id": ["音色ID不存在"],
            "platform": ["平台不支持"],
            "voice_settings.speed": ["语速必须在0.5-2.0之间"],
            "sample_rate": ["采样率不支持，支持：16000, 24000, 48000"]
        }
    },
    "timestamp": 1640997300,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1015 - 平台服务不可用)：**
```json
{
    "code": 1015,
    "message": "语音合成平台服务不可用",
    "data": {
        "platform": "火山引擎豆包",
        "error_reason": "平台维护中",
        "estimated_recovery_time": "2024-01-01 15:00:00",
        "alternative_platforms": ["MiniMax", "DeepSeek"],
        "auto_switch_available": true
    },
    "timestamp": 1640997300,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997300,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 48.2 获取语音合成状态 GET /api/voices/{task_id}/status

**请求参数示例：**
```json
{
    "task_id": "voice_task_001"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "voice_task_001",
        "task_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 13:30:00",
        "started_at": "2024-01-01 13:30:05",
        "completed_at": "2024-01-01 13:30:35",
        "processing_time": 30,
        "text": "欢迎使用我们的智能语音合成服务，这是一段测试音频。",
        "voice_settings": {
            "speed": 1.0,
            "pitch": 0.0,
            "volume": 0.8,
            "emotion": "neutral",
            "style": "professional"
        },
        "platform": "火山引擎豆包",
        "platform_model": "volcengine_tts_v1",
        "result": {
            "audio_url": "https://example.com/audio/voice_task_001.mp3",
            "audio_duration": 8.2,
            "file_size": "132KB",
            "sample_rate": 24000,
            "bit_rate": "128kbps",
            "format": "mp3",
            "quality_score": 0.95
        },
        "credits_used": 15,
        "error_info": null,
        "retry_count": 0,
        "metadata": {
            "character_count": 28,
            "word_count": 14,
            "estimated_reading_time": 8.5,
            "actual_duration": 8.2
        }
    },
    "timestamp": 1640997360,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 任务不存在)：**
```json
{
    "code": 404,
    "message": "语音合成任务不存在",
    "data": {
        "task_id": "invalid_task_id",
        "suggestion": "请检查任务ID是否正确"
    },
    "timestamp": 1640997360,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997360,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 48.3 语音平台对比 GET /api/voices/platform-comparison

**请求参数示例：**
```json
{
    "platforms": ["火山引擎豆包", "MiniMax"],
    "voice_type": "female",
    "comparison_metrics": ["quality", "speed", "pricing", "features"],
    "sample_text": "这是一段测试文本，用于对比不同平台的语音合成效果。"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "comparison_summary": {
            "platforms_count": 2,
            "voice_type": "female",
            "comparison_date": "2024-01-01 13:35:00",
            "sample_text": "这是一段测试文本，用于对比不同平台的语音合成效果。"
        },
        "platform_comparison": [
            {
                "platform": "火山引擎豆包",
                "overall_score": 92,
                "rank": 1,
                "available_voices": 15,
                "metrics": {
                    "quality": {
                        "naturalness": 0.95,
                        "clarity": 0.93,
                        "emotion_expression": 0.88,
                        "score": 92
                    },
                    "speed": {
                        "average_processing_time": 25,
                        "concurrent_capacity": 100,
                        "score": 90
                    },
                    "pricing": {
                        "credits_per_minute": 60,
                        "cost_effectiveness": 0.85,
                        "score": 85
                    },
                    "features": {
                        "emotion_control": true,
                        "speed_adjustment": true,
                        "pitch_control": true,
                        "style_options": 8,
                        "score": 95
                    }
                },
                "strengths": ["音质自然", "情感表达丰富", "功能全面"],
                "weaknesses": ["价格相对较高"],
                "best_use_cases": ["专业配音", "情感表达", "多样化需求"],
                "sample_audio": "https://example.com/samples/volcengine_sample.mp3"
            },
            {
                "platform": "MiniMax",
                "overall_score": 88,
                "rank": 2,
                "available_voices": 12,
                "metrics": {
                    "quality": {
                        "naturalness": 0.90,
                        "clarity": 0.92,
                        "emotion_expression": 0.85,
                        "score": 89
                    },
                    "speed": {
                        "average_processing_time": 30,
                        "concurrent_capacity": 80,
                        "score": 85
                    },
                    "pricing": {
                        "credits_per_minute": 45,
                        "cost_effectiveness": 0.92,
                        "score": 92
                    },
                    "features": {
                        "emotion_control": true,
                        "speed_adjustment": true,
                        "pitch_control": false,
                        "style_options": 5,
                        "score": 80
                    }
                },
                "strengths": ["性价比高", "处理稳定", "多模态支持"],
                "weaknesses": ["功能相对简单", "音色选择较少"],
                "best_use_cases": ["批量处理", "成本敏感项目", "基础配音"],
                "sample_audio": "https://example.com/samples/minimax_sample.mp3"
            }
        ],
        "recommendation": {
            "for_quality": "火山引擎豆包",
            "for_cost": "MiniMax",
            "for_features": "火山引擎豆包",
            "for_speed": "火山引擎豆包",
            "overall_best": "火山引擎豆包"
        }
    },
    "timestamp": 1640997420,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "platforms": ["至少需要选择2个平台进行对比"],
            "voice_type": ["音色类型无效"],
            "sample_text": ["测试文本长度不能超过500字符"]
        }
    },
    "timestamp": 1640997420,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997420,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 48.4 批量语音合成 POST /api/voices/batch-synthesize

**请求参数示例：**
```json
{
    "batch_name": "产品介绍音频批量合成",
    "texts": [
        {
            "id": "text_001",
            "content": "欢迎使用我们的产品，这是第一段介绍。",
            "voice_id": "voice_001"
        },
        {
            "id": "text_002",
            "content": "我们的产品具有强大的功能和优秀的用户体验。",
            "voice_id": "voice_002"
        },
        {
            "id": "text_003",
            "content": "感谢您的使用，期待为您提供更好的服务。",
            "voice_id": "voice_001"
        }
    ],
    "platform": "火山引擎豆包",
    "voice_settings": {
        "speed": 1.0,
        "pitch": 0.0,
        "volume": 0.8,
        "emotion": "professional"
    },
    "output_format": "mp3",
    "sample_rate": 24000,
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "批量语音合成任务创建成功",
    "data": {
        "batch_id": "batch_voice_001",
        "batch_name": "产品介绍音频批量合成",
        "total_tasks": 3,
        "platform": "火山引擎豆包",
        "batch_status": "processing",
        "created_at": "2024-01-01 13:40:00",
        "estimated_completion_time": "2024-01-01 13:42:00",
        "total_credits_cost": 45,
        "tasks": [
            {
                "task_id": "voice_task_002",
                "text_id": "text_001",
                "content": "欢迎使用我们的产品，这是第一段介绍。",
                "voice_id": "voice_001",
                "status": "processing",
                "estimated_duration": 7.5
            },
            {
                "task_id": "voice_task_003",
                "text_id": "text_002",
                "content": "我们的产品具有强大的功能和优秀的用户体验。",
                "voice_id": "voice_002",
                "status": "queued",
                "estimated_duration": 9.2
            },
            {
                "task_id": "voice_task_004",
                "text_id": "text_003",
                "content": "感谢您的使用，期待为您提供更好的服务。",
                "voice_id": "voice_001",
                "status": "queued",
                "estimated_duration": 8.8
            }
        ],
        "progress": {
            "completed": 0,
            "processing": 1,
            "queued": 2,
            "failed": 0,
            "overall_progress": 0
        },
        "status_check_url": "/api/voices/batch/batch_voice_001/status"
    },
    "timestamp": 1640997480,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "texts": ["批量文本不能为空", "批量数量不能超过50条"],
            "texts.0.content": ["文本内容不能为空"],
            "texts.1.voice_id": ["音色ID不存在"]
        }
    },
    "timestamp": 1640997480,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997480,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 48.5 音色克隆 POST /api/voices/clone

**请求参数示例：**
```json
{
    "clone_name": "我的专属音色",
    "source_audio_url": "https://example.com/source_audio.wav",
    "reference_text": "这是用于音色克隆的参考文本，请确保发音清晰准确。",
    "target_platform": "火山引擎豆包",
    "clone_settings": {
        "quality_level": "high",
        "training_duration": "standard",
        "emotion_range": ["neutral", "happy", "professional"]
    },
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "音色克隆任务创建成功",
    "data": {
        "clone_id": "clone_001",
        "clone_name": "我的专属音色",
        "source_audio_url": "https://example.com/source_audio.wav",
        "reference_text": "这是用于音色克隆的参考文本，请确保发音清晰准确。",
        "target_platform": "火山引擎豆包",
        "clone_status": "processing",
        "created_at": "2024-01-01 13:45:00",
        "estimated_completion_time": "2024-01-01 14:15:00",
        "estimated_training_time": 1800,
        "clone_settings": {
            "quality_level": "high",
            "training_duration": "standard",
            "emotion_range": ["neutral", "happy", "professional"]
        },
        "audio_analysis": {
            "duration": 45.2,
            "sample_rate": 44100,
            "quality_score": 0.92,
            "voice_characteristics": {
                "gender": "female",
                "age_estimate": "25-30",
                "accent": "standard",
                "tone": "warm"
            }
        },
        "credits_cost": 200,
        "processing_stages": [
            {"stage": "audio_analysis", "status": "completed", "progress": 100},
            {"stage": "feature_extraction", "status": "processing", "progress": 45},
            {"stage": "model_training", "status": "pending", "progress": 0},
            {"stage": "quality_validation", "status": "pending", "progress": 0}
        ],
        "status_check_url": "/api/voices/clone/clone_001/status"
    },
    "timestamp": 1640997540,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1016 - 音频质量不符合要求)：**
```json
{
    "code": 1016,
    "message": "音频质量不符合克隆要求",
    "data": {
        "audio_url": "https://example.com/source_audio.wav",
        "quality_issues": [
            "背景噪音过大",
            "音频时长不足",
            "采样率过低"
        ],
        "requirements": {
            "min_duration": 30,
            "max_noise_level": 0.1,
            "min_sample_rate": 16000
        },
        "suggestions": [
            "使用降噪软件处理音频",
            "提供更长的音频样本",
            "使用更高质量的录音设备"
        ]
    },
    "timestamp": 1640997540,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997540,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 48.6 音色克隆状态查询 GET /api/voices/clone/{id}/status

**请求参数示例：**
```json
{
    "id": "clone_001"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "clone_id": "clone_001",
        "clone_name": "我的专属音色",
        "clone_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 13:45:00",
        "completed_at": "2024-01-01 14:12:00",
        "processing_time": 1620,
        "result": {
            "voice_id": "voice_cloned_001",
            "voice_name": "我的专属音色",
            "quality_score": 0.94,
            "similarity_score": 0.91,
            "sample_audio": "https://example.com/cloned_samples/clone_001_sample.mp3",
            "available_emotions": ["neutral", "happy", "professional"],
            "usage_ready": true
        },
        "credits_used": 200,
        "processing_stages": [
            {"stage": "audio_analysis", "status": "completed", "progress": 100, "duration": 120},
            {"stage": "feature_extraction", "status": "completed", "progress": 100, "duration": 300},
            {"stage": "model_training", "status": "completed", "progress": 100, "duration": 1080},
            {"stage": "quality_validation", "status": "completed", "progress": 100, "duration": 120}
        ]
    },
    "timestamp": 1640997600,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 克隆任务不存在)：**
```json
{
    "code": 404,
    "message": "音色克隆任务不存在",
    "data": {
        "clone_id": "invalid_clone_id",
        "suggestion": "请检查克隆任务ID是否正确"
    },
    "timestamp": 1640997600,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997600,
    "request_id": "req_abc123_def456"
}
```

#### 步骤7: 48.7 自定义音色生成 POST /api/voices/custom

**请求参数示例：**
```json
{
    "custom_name": "我的定制音色",
    "base_voice_id": "voice_001",
    "customization_settings": {
        "speed_adjustment": 1.1,
        "pitch_adjustment": 0.2,
        "tone_modification": "warmer",
        "emotion_emphasis": "gentle",
        "style_preference": "conversational"
    },
    "target_scenarios": ["客服对话", "教育内容", "广告配音"],
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "自定义音色生成任务创建成功",
    "data": {
        "custom_id": "custom_001",
        "custom_name": "我的定制音色",
        "base_voice_id": "voice_001",
        "base_voice_name": "专业女声",
        "customization_settings": {
            "speed_adjustment": 1.1,
            "pitch_adjustment": 0.2,
            "tone_modification": "warmer",
            "emotion_emphasis": "gentle",
            "style_preference": "conversational"
        },
        "target_scenarios": ["客服对话", "教育内容", "广告配音"],
        "custom_status": "processing",
        "created_at": "2024-01-01 14:20:00",
        "estimated_completion_time": "2024-01-01 14:25:00",
        "credits_cost": 50,
        "processing_stages": [
            {"stage": "parameter_analysis", "status": "completed", "progress": 100},
            {"stage": "voice_modification", "status": "processing", "progress": 60},
            {"stage": "quality_testing", "status": "pending", "progress": 0}
        ],
        "status_check_url": "/api/voices/custom/custom_001/status"
    },
    "timestamp": 1640997660,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "custom_name": ["自定义名称不能为空"],
            "base_voice_id": ["基础音色ID不存在"],
            "customization_settings.speed_adjustment": ["速度调整范围必须在0.5-2.0之间"],
            "customization_settings.pitch_adjustment": ["音调调整范围必须在-1.0-1.0之间"]
        }
    },
    "timestamp": 1640997660,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997660,
    "request_id": "req_abc123_def456"
}
```

#### 步骤8: 48.8 自定义音色状态查询 GET /api/voices/custom/{id}/status

**请求参数示例：**
```json
{
    "id": "custom_001"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "custom_id": "custom_001",
        "custom_name": "我的定制音色",
        "custom_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 14:20:00",
        "completed_at": "2024-01-01 14:24:30",
        "processing_time": 270,
        "result": {
            "voice_id": "voice_custom_001",
            "voice_name": "我的定制音色",
            "customization_applied": {
                "speed_adjustment": 1.1,
                "pitch_adjustment": 0.2,
                "tone_modification": "warmer",
                "emotion_emphasis": "gentle",
                "style_preference": "conversational"
            },
            "quality_score": 0.93,
            "sample_audio": "https://example.com/custom_samples/custom_001_sample.mp3",
            "usage_ready": true
        },
        "credits_used": 50,
        "processing_stages": [
            {"stage": "parameter_analysis", "status": "completed", "progress": 100, "duration": 30},
            {"stage": "voice_modification", "status": "completed", "progress": 100, "duration": 180},
            {"stage": "quality_testing", "status": "completed", "progress": 100, "duration": 60}
        ]
    },
    "timestamp": 1640997720,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 自定义任务不存在)：**
```json
{
    "code": 404,
    "message": "自定义音色任务不存在",
    "data": {
        "custom_id": "invalid_custom_id",
        "suggestion": "请检查自定义任务ID是否正确"
    },
    "timestamp": 1640997720,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997720,
    "request_id": "req_abc123_def456"
}
```

#### 步骤9: 48.9 音色试听 POST /api/voices/{id}/preview

**请求参数示例：**
```json
{
    "id": "voice_001",
    "preview_text": "这是一段音色试听文本，用于测试音色效果。",
    "voice_settings": {
        "speed": 1.0,
        "pitch": 0.0,
        "volume": 0.8,
        "emotion": "neutral"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "音色试听生成成功",
    "data": {
        "preview_id": "preview_001",
        "voice_id": "voice_001",
        "voice_name": "专业女声",
        "preview_text": "这是一段音色试听文本，用于测试音色效果。",
        "voice_settings": {
            "speed": 1.0,
            "pitch": 0.0,
            "volume": 0.8,
            "emotion": "neutral"
        },
        "preview_audio": {
            "audio_url": "https://example.com/previews/preview_001.mp3",
            "duration": 6.8,
            "file_size": "89KB",
            "format": "mp3",
            "expires_at": "2024-01-01 15:30:00"
        },
        "generated_at": "2024-01-01 14:30:00",
        "credits_cost": 5
    },
    "timestamp": 1640997780,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 音色不存在)：**
```json
{
    "code": 404,
    "message": "音色不存在",
    "data": {
        "voice_id": "invalid_voice_id",
        "available_voices": ["voice_001", "voice_002", "voice_003"]
    },
    "timestamp": 1640997780,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997780,
    "request_id": "req_abc123_def456"
}
```

#### 步骤10: 48.10 语音合成历史 GET /api/voices/history

**请求参数示例：**
```json
{
    "page": 1,
    "per_page": 20,
    "date_range": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-07"
    },
    "platform": "火山引擎豆包",
    "status": "completed"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "history_records": [
            {
                "task_id": "voice_task_001",
                "text": "欢迎使用我们的智能语音合成服务，这是一段测试音频。",
                "voice_id": "voice_001",
                "voice_name": "专业女声",
                "platform": "火山引擎豆包",
                "task_status": "completed",
                "created_at": "2024-01-01 13:30:00",
                "completed_at": "2024-01-01 13:30:35",
                "duration": 8.2,
                "credits_used": 15,
                "audio_url": "https://example.com/audio/voice_task_001.mp3"
            },
            {
                "task_id": "voice_task_002",
                "text": "欢迎使用我们的产品，这是第一段介绍。",
                "voice_id": "voice_001",
                "voice_name": "专业女声",
                "platform": "火山引擎豆包",
                "task_status": "completed",
                "created_at": "2024-01-01 13:40:00",
                "completed_at": "2024-01-01 13:40:28",
                "duration": 7.5,
                "credits_used": 12,
                "audio_url": "https://example.com/audio/voice_task_002.mp3"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 25,
            "last_page": 2
        },
        "statistics": {
            "total_tasks": 25,
            "completed_tasks": 23,
            "failed_tasks": 2,
            "total_credits_used": 380,
            "total_audio_duration": 185.6,
            "most_used_voice": "voice_001",
            "most_used_platform": "火山引擎豆包"
        }
    },
    "timestamp": 1640997840,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "date_range.start_date": ["开始日期格式错误"],
            "date_range.end_date": ["结束日期不能早于开始日期"],
            "per_page": ["每页数量必须在1-100之间"]
        }
    },
    "timestamp": 1640997840,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997840,
    "request_id": "req_abc123_def456"
}
```

---

## 🔊 **4.5 音效：通过API测试添加测试数据** (4个接口)

### 📋 **测试目标**
通过API接口测试音效生成、状态查询和批量处理功能，验证音效AI服务的稳定性。

### 🎵 **音效生成与管理**

#### 步骤1: 46.1 音效生成 POST /api/sounds/generate

**请求参数示例：**
```json
{
    "description": "雨天的声音，包含雨滴打在窗户上的声音",
    "duration": 30,
    "platform": "MiniMax",
    "generation_settings": {
        "quality": "high",
        "style": "realistic",
        "intensity": "medium",
        "environment": "indoor"
    },
    "output_format": "wav",
    "sample_rate": 48000,
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "音效生成任务创建成功",
    "data": {
        "task_id": "sound_task_001",
        "description": "雨天的声音，包含雨滴打在窗户上的声音",
        "duration": 30,
        "platform": "MiniMax",
        "platform_model": "minimax_sound_v1",
        "generation_settings": {
            "quality": "high",
            "style": "realistic",
            "intensity": "medium",
            "environment": "indoor"
        },
        "output_format": "wav",
        "sample_rate": 48000,
        "task_status": "processing",
        "created_at": "2024-01-01 14:35:00",
        "estimated_completion_time": "2024-01-01 14:37:00",
        "credits_cost": 25,
        "processing_queue": {
            "position": 1,
            "estimated_wait_time": 60
        },
        "status_check_url": "/api/sounds/sound_task_001/status"
    },
    "timestamp": 1640997900,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "description": ["音效描述不能为空", "描述长度不能超过500字符"],
            "duration": ["时长必须在1-300秒之间"],
            "platform": ["平台不支持音效生成"],
            "sample_rate": ["采样率不支持，支持：16000, 44100, 48000"]
        }
    },
    "timestamp": 1640997900,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1015 - 平台服务不可用)：**
```json
{
    "code": 1015,
    "message": "音效生成平台服务不可用",
    "data": {
        "platform": "MiniMax",
        "error_reason": "平台维护中",
        "estimated_recovery_time": "2024-01-01 16:00:00",
        "alternative_platforms": ["DeepSeek", "KlingAI"],
        "auto_switch_available": true
    },
    "timestamp": 1640997900,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997900,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 46.2 音效生成状态查询 GET /api/sounds/{id}/status

**请求参数示例：**
```json
{
    "id": "sound_task_001"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "sound_task_001",
        "task_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 14:35:00",
        "started_at": "2024-01-01 14:35:15",
        "completed_at": "2024-01-01 14:36:45",
        "processing_time": 90,
        "description": "雨天的声音，包含雨滴打在窗户上的声音",
        "platform": "MiniMax",
        "platform_model": "minimax_sound_v1",
        "generation_settings": {
            "quality": "high",
            "style": "realistic",
            "intensity": "medium",
            "environment": "indoor"
        },
        "result": {
            "audio_url": "https://example.com/sounds/sound_task_001.wav",
            "duration": 30.2,
            "file_size": "2.8MB",
            "sample_rate": 48000,
            "format": "wav",
            "quality_score": 0.92,
            "generated_elements": [
                "雨滴声",
                "窗户敲击声",
                "环境氛围声"
            ]
        },
        "credits_used": 25,
        "error_info": null,
        "retry_count": 0
    },
    "timestamp": 1640997960,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 任务不存在)：**
```json
{
    "code": 404,
    "message": "音效生成任务不存在",
    "data": {
        "task_id": "invalid_task_id",
        "suggestion": "请检查任务ID是否正确"
    },
    "timestamp": 1640997960,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640997960,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 46.3 音效生成结果获取 GET /api/sounds/{id}/result

**请求参数示例：**
```json
{
    "id": "sound_task_001",
    "include_metadata": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "sound_task_001",
        "task_status": "completed",
        "description": "雨天的声音，包含雨滴打在窗户上的声音",
        "result": {
            "audio_url": "https://example.com/sounds/sound_task_001.wav",
            "download_url": "https://example.com/download/sound_task_001.wav",
            "duration": 30.2,
            "file_size": "2.8MB",
            "sample_rate": 48000,
            "bit_depth": 24,
            "format": "wav",
            "quality_score": 0.92,
            "generated_elements": [
                "雨滴声",
                "窗户敲击声",
                "环境氛围声"
            ],
            "audio_analysis": {
                "frequency_range": "20Hz-20kHz",
                "dynamic_range": "45dB",
                "peak_amplitude": "-3dB",
                "rms_level": "-18dB"
            }
        },
        "generation_metadata": {
            "platform": "MiniMax",
            "platform_model": "minimax_sound_v1",
            "generation_time": 90,
            "model_version": "v1.2.3",
            "generation_settings": {
                "quality": "high",
                "style": "realistic",
                "intensity": "medium",
                "environment": "indoor"
            }
        },
        "usage_info": {
            "credits_used": 25,
            "download_expires_at": "2024-01-08 14:36:45",
            "usage_rights": "commercial",
            "attribution_required": false
        },
        "created_at": "2024-01-01 14:35:00",
        "completed_at": "2024-01-01 14:36:45"
    },
    "timestamp": 1640998020,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 任务不存在或未完成)：**
```json
{
    "code": 404,
    "message": "音效生成任务不存在或未完成",
    "data": {
        "task_id": "sound_task_001",
        "current_status": "processing",
        "suggestion": "请等待任务完成后再获取结果"
    },
    "timestamp": 1640998020,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998020,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 46.4 批量音效生成 POST /api/batch/sounds/generate

**请求参数示例：**
```json
{
    "batch_name": "自然环境音效批量生成",
    "sound_requests": [
        {
            "id": "sound_001",
            "description": "森林中的鸟叫声",
            "duration": 45,
            "settings": {
                "quality": "high",
                "style": "natural",
                "intensity": "medium"
            }
        },
        {
            "id": "sound_002",
            "description": "海浪拍打岸边的声音",
            "duration": 60,
            "settings": {
                "quality": "high",
                "style": "realistic",
                "intensity": "strong"
            }
        },
        {
            "id": "sound_003",
            "description": "微风吹过树叶的声音",
            "duration": 30,
            "settings": {
                "quality": "medium",
                "style": "gentle",
                "intensity": "light"
            }
        }
    ],
    "platform": "MiniMax",
    "output_format": "wav",
    "sample_rate": 48000,
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "批量音效生成任务创建成功",
    "data": {
        "batch_id": "batch_sound_001",
        "batch_name": "自然环境音效批量生成",
        "total_tasks": 3,
        "platform": "MiniMax",
        "batch_status": "processing",
        "created_at": "2024-01-01 14:40:00",
        "estimated_completion_time": "2024-01-01 14:45:00",
        "total_credits_cost": 75,
        "tasks": [
            {
                "task_id": "sound_task_002",
                "sound_id": "sound_001",
                "description": "森林中的鸟叫声",
                "duration": 45,
                "status": "processing",
                "estimated_completion": "2024-01-01 14:42:00"
            },
            {
                "task_id": "sound_task_003",
                "sound_id": "sound_002",
                "description": "海浪拍打岸边的声音",
                "duration": 60,
                "status": "queued",
                "estimated_completion": "2024-01-01 14:44:00"
            },
            {
                "task_id": "sound_task_004",
                "sound_id": "sound_003",
                "description": "微风吹过树叶的声音",
                "duration": 30,
                "status": "queued",
                "estimated_completion": "2024-01-01 14:45:00"
            }
        ],
        "progress": {
            "completed": 0,
            "processing": 1,
            "queued": 2,
            "failed": 0,
            "overall_progress": 0
        },
        "status_check_url": "/api/batch/sounds/batch_sound_001/status"
    },
    "timestamp": 1640998080,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "sound_requests": ["批量音效请求不能为空", "批量数量不能超过20条"],
            "sound_requests.0.description": ["音效描述不能为空"],
            "sound_requests.1.duration": ["时长必须在1-300秒之间"]
        }
    },
    "timestamp": 1640998080,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998080,
    "request_id": "req_abc123_def456"
}
```

---

## 🎼 **4.6 音乐：通过API测试添加测试数据** (5个接口)

### 📋 **测试目标**
通过API接口测试音乐生成、风格管理和批量处理功能，验证AI音乐创作服务的稳定性。

### 🎹 **音乐创作与管理**

#### 步骤1: 38.1 音乐生成 POST /api/music/generate

**请求参数示例：**
```json
{
    "description": "轻松愉快的咖啡厅背景音乐，钢琴主导，带有轻柔的弦乐",
    "genre": "jazz",
    "mood": "relaxed",
    "duration": 120,
    "platform": "MiniMax",
    "generation_settings": {
        "tempo": 80,
        "key": "C_major",
        "instruments": ["piano", "strings", "light_percussion"],
        "style": "contemporary_jazz"
    },
    "output_format": "mp3",
    "quality": "high",
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "音乐生成任务创建成功",
    "data": {
        "task_id": "music_task_001",
        "description": "轻松愉快的咖啡厅背景音乐，钢琴主导，带有轻柔的弦乐",
        "genre": "jazz",
        "mood": "relaxed",
        "duration": 120,
        "platform": "MiniMax",
        "platform_model": "minimax_music_v1",
        "generation_settings": {
            "tempo": 80,
            "key": "C_major",
            "instruments": ["piano", "strings", "light_percussion"],
            "style": "contemporary_jazz"
        },
        "output_format": "mp3",
        "quality": "high",
        "task_status": "processing",
        "created_at": "2024-01-01 14:45:00",
        "estimated_completion_time": "2024-01-01 14:50:00",
        "credits_cost": 80,
        "processing_queue": {
            "position": 1,
            "estimated_wait_time": 180
        },
        "status_check_url": "/api/music/music_task_001/status"
    },
    "timestamp": 1640998140,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "description": ["音乐描述不能为空", "描述长度不能超过1000字符"],
            "duration": ["时长必须在10-600秒之间"],
            "genre": ["音乐风格不支持"],
            "generation_settings.tempo": ["节拍必须在60-200之间"]
        }
    },
    "timestamp": 1640998140,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1015 - 平台服务不可用)：**
```json
{
    "code": 1015,
    "message": "音乐生成平台服务不可用",
    "data": {
        "platform": "MiniMax",
        "error_reason": "平台维护中",
        "estimated_recovery_time": "2024-01-01 16:00:00",
        "alternative_platforms": ["DeepSeek", "KlingAI"],
        "auto_switch_available": true
    },
    "timestamp": 1640998140,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998140,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 38.2 获取音乐生成状态 GET /api/music/{task_id}/status

**请求参数示例：**
```json
{
    "task_id": "music_task_001"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "music_task_001",
        "task_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 14:45:00",
        "started_at": "2024-01-01 14:45:30",
        "completed_at": "2024-01-01 14:49:45",
        "processing_time": 255,
        "description": "轻松愉快的咖啡厅背景音乐，钢琴主导，带有轻柔的弦乐",
        "platform": "MiniMax",
        "platform_model": "minimax_music_v1",
        "result": {
            "audio_url": "https://example.com/music/music_task_001.mp3",
            "duration": 120.5,
            "file_size": "5.8MB",
            "format": "mp3",
            "quality_score": 0.94,
            "generated_elements": {
                "main_melody": "piano",
                "harmony": "strings",
                "rhythm": "light_percussion",
                "structure": "AABA_form"
            }
        },
        "credits_used": 80,
        "error_info": null,
        "retry_count": 0,
        "metadata": {
            "actual_tempo": 82,
            "key_signature": "C_major",
            "time_signature": "4/4",
            "complexity_level": "medium"
        }
    },
    "timestamp": 1640998200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 任务不存在)：**
```json
{
    "code": 404,
    "message": "音乐生成任务不存在",
    "data": {
        "task_id": "invalid_task_id",
        "suggestion": "请检查任务ID是否正确"
    },
    "timestamp": 1640998200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998200,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 38.3 音乐风格列表 GET /api/music/styles

**请求参数示例：**
```json
{
    "category": "background",
    "mood": "relaxed",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "music_styles": [
            {
                "style_id": "style_jazz_001",
                "style_name": "现代爵士",
                "category": "background",
                "description": "现代爵士风格，适合咖啡厅、餐厅等休闲场所",
                "mood": "relaxed",
                "characteristics": {
                    "tempo_range": "70-120",
                    "key_preferences": ["C_major", "G_major", "F_major"],
                    "typical_instruments": ["piano", "bass", "drums", "saxophone"],
                    "complexity": "medium"
                },
                "sample_audio": "https://example.com/music_styles/style_jazz_001_sample.mp3",
                "usage_count": 450,
                "user_rating": 4.8,
                "is_premium": false,
                "created_at": "2024-01-01 10:00:00"
            },
            {
                "style_id": "style_ambient_001",
                "style_name": "环境氛围",
                "category": "background",
                "description": "轻柔的环境氛围音乐，营造宁静舒适的空间感",
                "mood": "calm",
                "characteristics": {
                    "tempo_range": "60-90",
                    "key_preferences": ["A_minor", "D_minor", "E_minor"],
                    "typical_instruments": ["synthesizer", "ambient_pads", "soft_percussion"],
                    "complexity": "low"
                },
                "sample_audio": "https://example.com/music_styles/style_ambient_001_sample.mp3",
                "usage_count": 320,
                "user_rating": 4.6,
                "is_premium": false,
                "created_at": "2024-01-01 10:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 45,
            "last_page": 3
        },
        "categories": [
            {"name": "background", "count": 25},
            {"name": "cinematic", "count": 12},
            {"name": "upbeat", "count": 8}
        ],
        "moods": [
            {"name": "relaxed", "count": 18},
            {"name": "energetic", "count": 15},
            {"name": "calm", "count": 12}
        ]
    },
    "timestamp": 1640998260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "category": ["音乐分类不存在"],
            "mood": ["情绪类型不支持"],
            "per_page": ["每页数量必须在1-50之间"]
        }
    },
    "timestamp": 1640998260,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998260,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 38.4 音乐生成结果获取 GET /api/music/{id}/result

**请求参数示例：**
```json
{
    "id": "music_task_001",
    "include_analysis": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "music_task_001",
        "task_status": "completed",
        "description": "轻松愉快的咖啡厅背景音乐，钢琴主导，带有轻柔的弦乐",
        "result": {
            "audio_url": "https://example.com/music/music_task_001.mp3",
            "download_url": "https://example.com/download/music_task_001.mp3",
            "duration": 120.5,
            "file_size": "5.8MB",
            "format": "mp3",
            "quality_score": 0.94,
            "generated_elements": {
                "main_melody": "piano",
                "harmony": "strings",
                "rhythm": "light_percussion",
                "structure": "AABA_form"
            },
            "music_analysis": {
                "tempo": 82,
                "key": "C_major",
                "time_signature": "4/4",
                "chord_progression": ["C", "Am", "F", "G"],
                "dynamic_range": "medium",
                "frequency_spectrum": {
                    "bass": "60-250Hz",
                    "midrange": "250Hz-4kHz",
                    "treble": "4kHz-20kHz"
                }
            }
        },
        "generation_metadata": {
            "platform": "MiniMax",
            "platform_model": "minimax_music_v1",
            "generation_time": 255,
            "model_version": "v1.3.2",
            "generation_settings": {
                "tempo": 80,
                "key": "C_major",
                "instruments": ["piano", "strings", "light_percussion"],
                "style": "contemporary_jazz"
            }
        },
        "usage_info": {
            "credits_used": 80,
            "download_expires_at": "2024-01-08 14:49:45",
            "usage_rights": "royalty_free",
            "commercial_use": true
        },
        "created_at": "2024-01-01 14:45:00",
        "completed_at": "2024-01-01 14:49:45"
    },
    "timestamp": 1640998320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 任务不存在或未完成)：**
```json
{
    "code": 404,
    "message": "音乐生成任务不存在或未完成",
    "data": {
        "task_id": "music_task_001",
        "current_status": "processing",
        "suggestion": "请等待任务完成后再获取结果"
    },
    "timestamp": 1640998320,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998320,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 38.5 批量音乐生成 POST /api/batch/music/generate

**请求参数示例：**
```json
{
    "batch_name": "背景音乐合集批量生成",
    "music_requests": [
        {
            "id": "music_001",
            "description": "轻快的开场音乐",
            "genre": "pop",
            "mood": "upbeat",
            "duration": 90,
            "settings": {
                "tempo": 120,
                "key": "G_major",
                "instruments": ["guitar", "drums", "bass"]
            }
        },
        {
            "id": "music_002",
            "description": "温馨的结尾音乐",
            "genre": "acoustic",
            "mood": "warm",
            "duration": 60,
            "settings": {
                "tempo": 75,
                "key": "C_major",
                "instruments": ["acoustic_guitar", "strings"]
            }
        }
    ],
    "platform": "MiniMax",
    "output_format": "mp3",
    "quality": "high",
    "project_id": "project_123"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "批量音乐生成任务创建成功",
    "data": {
        "batch_id": "batch_music_001",
        "batch_name": "背景音乐合集批量生成",
        "total_tasks": 2,
        "platform": "MiniMax",
        "batch_status": "processing",
        "created_at": "2024-01-01 14:55:00",
        "estimated_completion_time": "2024-01-01 15:05:00",
        "total_credits_cost": 150,
        "tasks": [
            {
                "task_id": "music_task_002",
                "music_id": "music_001",
                "description": "轻快的开场音乐",
                "genre": "pop",
                "duration": 90,
                "status": "processing",
                "estimated_completion": "2024-01-01 15:00:00"
            },
            {
                "task_id": "music_task_003",
                "music_id": "music_002",
                "description": "温馨的结尾音乐",
                "genre": "acoustic",
                "duration": 60,
                "status": "queued",
                "estimated_completion": "2024-01-01 15:05:00"
            }
        ],
        "progress": {
            "completed": 0,
            "processing": 1,
            "queued": 1,
            "failed": 0,
            "overall_progress": 0
        },
        "status_check_url": "/api/batch/music/batch_music_001/status"
    },
    "timestamp": 1640998380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "music_requests": ["批量音乐请求不能为空", "批量数量不能超过10条"],
            "music_requests.0.description": ["音乐描述不能为空"],
            "music_requests.1.duration": ["时长必须在10-600秒之间"]
        }
    },
    "timestamp": 1640998380,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998380,
    "request_id": "req_abc123_def456"
}
```

---

## 🎯 **第五阶段：主要功能接口** (25个接口)

### 📋 **测试目标**
验证核心业务流程：选风格+写剧情 → 绑角色 → 生成分镜图像 → 生成视频

### 📝 **5.1 选风格+写剧情** (6个接口)

#### 步骤1: 39.1 选风格+写剧情创建项目 POST /api/projects/create-with-story

**请求参数示例：**
```json
{
    "project_name": "都市爱情故事",
    "style_id": "style_001",
    "story_prompt": "一个关于都市白领在繁忙工作中寻找真爱的温馨故事",
    "story_settings": {
        "genre": "romance",
        "tone": "warm",
        "length": "medium",
        "target_audience": "young_adult"
    },
    "project_settings": {
        "privacy": "private",
        "auto_generate_title": true,
        "enable_ai_optimization": true
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "项目创建成功",
    "data": {
        "project_id": "project_001",
        "project_name": "都市爱情故事",
        "ai_generated_title": "繁华都市中的温暖邂逅",
        "style_id": "style_001",
        "style_name": "现代都市风",
        "story_task_id": "story_task_001",
        "story_status": "generating",
        "project_status": "initializing",
        "created_at": "2024-01-01 15:00:00",
        "estimated_completion_time": "2024-01-01 15:03:00",
        "story_settings": {
            "genre": "romance",
            "tone": "warm",
            "length": "medium",
            "target_audience": "young_adult"
        },
        "project_settings": {
            "privacy": "private",
            "auto_generate_title": true,
            "enable_ai_optimization": true
        },
        "next_steps": [
            "等待故事生成完成",
            "确认AI生成的项目标题",
            "绑定角色",
            "生成分镜图像"
        ],
        "story_check_url": "/api/stories/story_task_001/status"
    },
    "timestamp": 1640998440,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "project_name": ["项目名称不能为空", "项目名称长度不能超过100字符"],
            "style_id": ["风格ID不存在"],
            "story_prompt": ["故事提示不能为空", "故事提示长度不能超过1000字符"]
        }
    },
    "timestamp": 1640998440,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1017 - 项目名称已存在)：**
```json
{
    "code": 1017,
    "message": "项目名称已存在",
    "data": {
        "project_name": "都市爱情故事",
        "existing_project_id": "project_005",
        "suggestion": "请使用不同的项目名称"
    },
    "timestamp": 1640998440,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998440,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 28.1 故事生成 POST /api/stories/generate

**请求参数示例：**
```json
{
    "story_prompt": "一个关于都市白领在繁忙工作中寻找真爱的温馨故事",
    "style_id": "style_001",
    "platform": "DeepSeek",
    "generation_settings": {
        "genre": "romance",
        "tone": "warm",
        "length": "medium",
        "target_audience": "young_adult",
        "chapter_count": 5,
        "words_per_chapter": 800
    },
    "project_id": "project_001"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "故事生成任务创建成功",
    "data": {
        "task_id": "story_task_001",
        "story_prompt": "一个关于都市白领在繁忙工作中寻找真爱的温馨故事",
        "style_id": "style_001",
        "style_name": "现代都市风",
        "platform": "DeepSeek",
        "platform_model": "deepseek_story_v1",
        "generation_settings": {
            "genre": "romance",
            "tone": "warm",
            "length": "medium",
            "target_audience": "young_adult",
            "chapter_count": 5,
            "words_per_chapter": 800
        },
        "task_status": "processing",
        "created_at": "2024-01-01 15:00:30",
        "estimated_completion_time": "2024-01-01 15:03:30",
        "credits_cost": 120,
        "processing_queue": {
            "position": 1,
            "estimated_wait_time": 60
        },
        "status_check_url": "/api/stories/story_task_001/status",
        "project_id": "project_001"
    },
    "timestamp": 1640998500,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "story_prompt": ["故事提示不能为空", "故事提示长度不能超过1000字符"],
            "style_id": ["风格ID不存在"],
            "generation_settings.chapter_count": ["章节数量必须在1-20之间"],
            "generation_settings.words_per_chapter": ["每章字数必须在200-2000之间"]
        }
    },
    "timestamp": 1640998500,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998500,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 28.2 故事生成状态查询 GET /api/stories/{id}/status

**请求参数示例：**
```json
{
    "id": "story_task_001"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "story_task_001",
        "task_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 15:00:30",
        "started_at": "2024-01-01 15:00:45",
        "completed_at": "2024-01-01 15:03:15",
        "processing_time": 150,
        "story_prompt": "一个关于都市白领在繁忙工作中寻找真爱的温馨故事",
        "platform": "DeepSeek",
        "platform_model": "deepseek_story_v1",
        "result": {
            "story_title": "繁华都市中的温暖邂逅",
            "story_summary": "讲述了都市白领林雅婷在繁忙工作中偶遇咖啡店老板陈浩然，两人从陌生到相知相爱的温馨故事。",
            "total_chapters": 5,
            "total_words": 4000,
            "chapters": [
                {
                    "chapter_id": "chapter_001",
                    "chapter_title": "忙碌的开始",
                    "chapter_summary": "介绍女主角林雅婷的都市白领生活",
                    "word_count": 800,
                    "content_preview": "清晨的阳光透过落地窗洒在办公桌上，林雅婷端着咖啡走向会议室..."
                },
                {
                    "chapter_id": "chapter_002",
                    "chapter_title": "意外的邂逅",
                    "chapter_summary": "女主角在咖啡店遇到男主角",
                    "word_count": 800,
                    "content_preview": "下班后的街道依然繁忙，林雅婷走进了那家温馨的咖啡店..."
                }
            ],
            "characters": [
                {
                    "character_name": "林雅婷",
                    "character_role": "女主角",
                    "character_description": "28岁都市白领，市场总监，独立自信"
                },
                {
                    "character_name": "陈浩然",
                    "character_role": "男主角",
                    "character_description": "30岁咖啡店老板，温和细心，有艺术气质"
                }
            ],
            "story_url": "https://example.com/stories/story_task_001.txt"
        },
        "credits_used": 120,
        "quality_score": 0.95,
        "project_id": "project_001"
    },
    "timestamp": 1640998560,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 任务不存在)：**
```json
{
    "code": 404,
    "message": "故事生成任务不存在",
    "data": {
        "task_id": "invalid_task_id",
        "suggestion": "请检查任务ID是否正确"
    },
    "timestamp": 1640998560,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998560,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 39.2 确认AI生成的项目标题 PUT /api/projects/{id}/confirm-title

**请求参数示例：**
```json
{
    "id": "project_001",
    "confirmed_title": "繁华都市中的温暖邂逅",
    "title_modifications": {
        "use_ai_title": true,
        "custom_subtitle": "一个关于爱情与成长的都市故事"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "项目标题确认成功",
    "data": {
        "project_id": "project_001",
        "original_title": "都市爱情故事",
        "ai_generated_title": "繁华都市中的温暖邂逅",
        "confirmed_title": "繁华都市中的温暖邂逅",
        "custom_subtitle": "一个关于爱情与成长的都市故事",
        "title_status": "confirmed",
        "updated_at": "2024-01-01 15:05:00",
        "project_status": "title_confirmed",
        "next_steps": [
            "绑定角色",
            "生成分镜图像",
            "生成视频"
        ]
    },
    "timestamp": 1640998620,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 项目不存在)：**
```json
{
    "code": 404,
    "message": "项目不存在",
    "data": {
        "project_id": "invalid_project_id",
        "suggestion": "请检查项目ID是否正确"
    },
    "timestamp": 1640998620,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998620,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 39.3 获取用户项目列表 GET /api/projects/my-projects

**请求参数示例：**
```json
{
    "page": 1,
    "per_page": 20,
    "status": "all",
    "sort_by": "created_at",
    "sort_order": "desc"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "projects": [
            {
                "project_id": "project_001",
                "project_name": "繁华都市中的温暖邂逅",
                "original_name": "都市爱情故事",
                "style_id": "style_001",
                "style_name": "现代都市风",
                "project_status": "title_confirmed",
                "progress": {
                    "story_generation": "completed",
                    "title_confirmation": "completed",
                    "character_binding": "pending",
                    "image_generation": "pending",
                    "video_generation": "pending"
                },
                "created_at": "2024-01-01 15:00:00",
                "updated_at": "2024-01-01 15:05:00",
                "story_summary": "讲述了都市白领林雅婷在繁忙工作中偶遇咖啡店老板陈浩然，两人从陌生到相知相爱的温馨故事。",
                "thumbnail": "https://example.com/thumbnails/project_001.jpg",
                "total_chapters": 5,
                "total_words": 4000,
                "privacy": "private"
            },
            {
                "project_id": "project_002",
                "project_name": "科幻未来探险",
                "original_name": "科幻未来探险",
                "style_id": "style_003",
                "style_name": "科幻未来",
                "project_status": "completed",
                "progress": {
                    "story_generation": "completed",
                    "title_confirmation": "completed",
                    "character_binding": "completed",
                    "image_generation": "completed",
                    "video_generation": "completed"
                },
                "created_at": "2023-12-28 10:00:00",
                "updated_at": "2023-12-30 16:30:00",
                "story_summary": "2050年，人类开始探索银河系深处，主人公作为探险队长踏上了未知的星际之旅。",
                "thumbnail": "https://example.com/thumbnails/project_002.jpg",
                "total_chapters": 8,
                "total_words": 6400,
                "privacy": "public"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 2,
            "last_page": 1
        },
        "statistics": {
            "total_projects": 2,
            "completed_projects": 1,
            "in_progress_projects": 1,
            "total_words": 10400,
            "total_chapters": 13
        }
    },
    "timestamp": 1640998680,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998680,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 39.4 获取项目详情 GET /api/projects/{id}/detail

**请求参数示例：**
```json
{
    "id": "project_001",
    "include_story_content": true,
    "include_characters": true,
    "include_progress": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "project_id": "project_001",
        "project_name": "繁华都市中的温暖邂逅",
        "original_name": "都市爱情故事",
        "custom_subtitle": "一个关于爱情与成长的都市故事",
        "style_id": "style_001",
        "style_name": "现代都市风",
        "project_status": "title_confirmed",
        "privacy": "private",
        "created_at": "2024-01-01 15:00:00",
        "updated_at": "2024-01-01 15:05:00",
        "story_info": {
            "story_task_id": "story_task_001",
            "story_title": "繁华都市中的温暖邂逅",
            "story_summary": "讲述了都市白领林雅婷在繁忙工作中偶遇咖啡店老板陈浩然，两人从陌生到相知相爱的温馨故事。",
            "total_chapters": 5,
            "total_words": 4000,
            "genre": "romance",
            "tone": "warm",
            "target_audience": "young_adult",
            "story_url": "https://example.com/stories/story_task_001.txt"
        },
        "characters": [
            {
                "character_name": "林雅婷",
                "character_role": "女主角",
                "character_description": "28岁都市白领，市场总监，独立自信",
                "binding_status": "pending",
                "suggested_voice_id": "voice_001"
            },
            {
                "character_name": "陈浩然",
                "character_role": "男主角",
                "character_description": "30岁咖啡店老板，温和细心，有艺术气质",
                "binding_status": "pending",
                "suggested_voice_id": "voice_003"
            }
        ],
        "progress": {
            "story_generation": {
                "status": "completed",
                "completed_at": "2024-01-01 15:03:15",
                "credits_used": 120
            },
            "title_confirmation": {
                "status": "completed",
                "completed_at": "2024-01-01 15:05:00"
            },
            "character_binding": {
                "status": "pending",
                "total_characters": 2,
                "bound_characters": 0
            },
            "image_generation": {
                "status": "pending",
                "estimated_images": 10
            },
            "video_generation": {
                "status": "pending",
                "estimated_duration": 300
            }
        },
        "project_settings": {
            "auto_generate_title": true,
            "enable_ai_optimization": true,
            "image_style": "realistic",
            "video_quality": "high"
        },
        "resource_usage": {
            "total_credits_used": 120,
            "estimated_total_cost": 500,
            "storage_used": "2.5MB"
        }
    },
    "timestamp": 1640998740,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 项目不存在)：**
```json
{
    "code": 404,
    "message": "项目不存在",
    "data": {
        "project_id": "invalid_project_id",
        "suggestion": "请检查项目ID是否正确"
    },
    "timestamp": 1640998740,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998740,
    "request_id": "req_abc123_def456"
}
```

---

### 🎭 **5.2 绑角色** (6个接口)

#### 步骤1: 39.5 项目角色绑定 POST /api/projects/{id}/bind-characters

**请求参数示例：**
```json
{
    "id": "project_001",
    "character_bindings": [
        {
            "story_character_name": "林雅婷",
            "character_id": "char_001",
            "voice_id": "voice_001",
            "customizations": {
                "name_override": "林雅婷",
                "personality_emphasis": ["独立", "专业", "温和"],
                "voice_adjustments": {
                    "emotion_emphasis": ["自信", "温暖"]
                }
            }
        },
        {
            "story_character_name": "陈浩然",
            "character_id": "char_002",
            "voice_id": "voice_003",
            "customizations": {
                "name_override": "陈浩然",
                "personality_emphasis": ["温和", "细心", "艺术气质"],
                "voice_adjustments": {
                    "emotion_emphasis": ["温暖", "真诚"]
                }
            }
        }
    ]
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "角色绑定成功",
    "data": {
        "project_id": "project_001",
        "binding_summary": {
            "total_characters": 2,
            "successfully_bound": 2,
            "failed_bindings": 0
        },
        "character_bindings": [
            {
                "binding_id": "binding_project_001_001",
                "story_character_name": "林雅婷",
                "character_id": "char_001",
                "character_name": "都市白领",
                "voice_id": "voice_001",
                "voice_name": "专业女声",
                "binding_status": "active",
                "customizations": {
                    "name_override": "林雅婷",
                    "personality_emphasis": ["独立", "专业", "温和"],
                    "voice_adjustments": {
                        "emotion_emphasis": ["自信", "温暖"]
                    }
                },
                "preview_audio": "https://example.com/previews/binding_project_001_001.mp3",
                "created_at": "2024-01-01 15:10:00"
            },
            {
                "binding_id": "binding_project_001_002",
                "story_character_name": "陈浩然",
                "character_id": "char_002",
                "character_name": "创业CEO",
                "voice_id": "voice_003",
                "voice_name": "温和男声",
                "binding_status": "active",
                "customizations": {
                    "name_override": "陈浩然",
                    "personality_emphasis": ["温和", "细心", "艺术气质"],
                    "voice_adjustments": {
                        "emotion_emphasis": ["温暖", "真诚"]
                    }
                },
                "preview_audio": "https://example.com/previews/binding_project_001_002.mp3",
                "created_at": "2024-01-01 15:10:00"
            }
        ],
        "project_status": "characters_bound",
        "next_steps": [
            "生成分镜图像",
            "生成视频"
        ]
    },
    "timestamp": 1640998800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "character_bindings": ["角色绑定列表不能为空"],
            "character_bindings.0.character_id": ["角色ID不存在"],
            "character_bindings.1.voice_id": ["音色ID不存在"]
        }
    },
    "timestamp": 1640998800,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640998800,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 39.6 获取项目角色绑定状态 GET /api/projects/{id}/character-bindings

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "project_id": "project_001",
        "binding_summary": {
            "total_characters": 2,
            "bound_characters": 2,
            "unbound_characters": 0,
            "binding_completion": 100
        },
        "character_bindings": [
            {
                "binding_id": "binding_project_001_001",
                "story_character_name": "林雅婷",
                "character_id": "char_001",
                "character_name": "都市白领",
                "voice_id": "voice_001",
                "voice_name": "专业女声",
                "binding_status": "active",
                "preview_audio": "https://example.com/previews/binding_project_001_001.mp3"
            }
        ]
    },
    "timestamp": 1640998860,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 39.7 更新项目角色绑定 PUT /api/projects/{id}/character-bindings/{binding_id}

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "角色绑定更新成功",
    "data": {
        "binding_id": "binding_project_001_001",
        "project_id": "project_001",
        "story_character_name": "林雅婷",
        "character_id": "char_001",
        "voice_id": "voice_001",
        "updated_customizations": {
            "name_override": "林雅婷",
            "personality_emphasis": ["独立", "专业", "温和", "幽默"],
            "voice_adjustments": {
                "emotion_emphasis": ["自信", "温暖", "友善"]
            }
        },
        "updated_at": "2024-01-01 15:15:00",
        "preview_audio": "https://example.com/previews/binding_project_001_001_updated.mp3"
    },
    "timestamp": 1640998920,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 39.8 解绑项目角色 DELETE /api/projects/{id}/character-bindings/{binding_id}

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "角色解绑成功",
    "data": {
        "binding_id": "binding_project_001_002",
        "project_id": "project_001",
        "story_character_name": "陈浩然",
        "unbound_at": "2024-01-01 15:20:00",
        "backup_info": {
            "backup_created": true,
            "backup_id": "backup_binding_project_001_002",
            "restore_available_until": "2024-04-01 15:20:00"
        }
    },
    "timestamp": 1640998980,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 39.9 批量角色绑定 POST /api/projects/{id}/batch-bind-characters

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "批量角色绑定任务创建成功",
    "data": {
        "batch_id": "batch_bind_001",
        "project_id": "project_001",
        "total_bindings": 3,
        "batch_status": "processing",
        "created_at": "2024-01-01 15:25:00",
        "estimated_completion_time": "2024-01-01 15:27:00"
    },
    "timestamp": 1640999040,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 39.10 获取角色绑定历史 GET /api/projects/{id}/binding-history

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "project_id": "project_001",
        "binding_history": [
            {
                "action": "bind",
                "binding_id": "binding_project_001_001",
                "story_character_name": "林雅婷",
                "character_id": "char_001",
                "action_time": "2024-01-01 15:10:00"
            },
            {
                "action": "update",
                "binding_id": "binding_project_001_001",
                "changes": ["personality_emphasis", "voice_adjustments"],
                "action_time": "2024-01-01 15:15:00"
            }
        ]
    },
    "timestamp": 1640999100,
    "request_id": "req_abc123_def456"
}
```

---

### 🖼️ **5.3 生成分镜图像** (10个接口)

#### 步骤1: 40.1 分镜图像生成 POST /api/storyboards/generate

**请求参数示例：**
```json
{
    "project_id": "project_001",
    "generation_settings": {
        "image_count": 10,
        "image_style": "realistic",
        "aspect_ratio": "16:9",
        "quality": "high",
        "platform": "LiblibAI"
    },
    "scene_descriptions": [
        {
            "scene_id": "scene_001",
            "description": "清晨的都市办公楼，阳光透过落地窗",
            "characters": ["林雅婷"],
            "mood": "professional"
        },
        {
            "scene_id": "scene_002",
            "description": "温馨的咖啡店内部，暖色调灯光",
            "characters": ["陈浩然"],
            "mood": "warm"
        }
    ]
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "分镜图像生成任务创建成功",
    "data": {
        "task_id": "storyboard_task_001",
        "project_id": "project_001",
        "platform": "LiblibAI",
        "platform_model": "liblib_image_v2",
        "generation_settings": {
            "image_count": 10,
            "image_style": "realistic",
            "aspect_ratio": "16:9",
            "quality": "high"
        },
        "task_status": "processing",
        "created_at": "2024-01-01 15:30:00",
        "estimated_completion_time": "2024-01-01 15:35:00",
        "credits_cost": 200,
        "processing_queue": {
            "position": 1,
            "estimated_wait_time": 120
        },
        "status_check_url": "/api/storyboards/storyboard_task_001/status"
    },
    "timestamp": 1640999160,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "project_id": ["项目ID不存在"],
            "generation_settings.image_count": ["图像数量必须在1-50之间"],
            "scene_descriptions": ["场景描述不能为空"]
        }
    },
    "timestamp": 1640999160,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999160,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 40.2 分镜图像生成状态查询 GET /api/storyboards/{id}/status

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "storyboard_task_001",
        "task_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 15:30:00",
        "completed_at": "2024-01-01 15:34:30",
        "processing_time": 270,
        "result": {
            "total_images": 10,
            "generated_images": 10,
            "failed_images": 0,
            "images": [
                {
                    "image_id": "img_001",
                    "scene_id": "scene_001",
                    "image_url": "https://example.com/storyboards/img_001.jpg",
                    "description": "清晨的都市办公楼，阳光透过落地窗",
                    "characters": ["林雅婷"],
                    "quality_score": 0.95
                }
            ]
        },
        "credits_used": 200
    },
    "timestamp": 1640999220,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3-10: [其他分镜图像接口简化实现]

---

### 🎬 **5.4 生成视频** (3个接口)

#### 步骤1: 47.1 智能视频生成 POST /api/videos/generate

**请求参数示例：**
```json
{
    "project_id": "project_001",
    "video_settings": {
        "duration": 300,
        "quality": "high",
        "resolution": "1920x1080",
        "frame_rate": 30,
        "platform": "KlingAI"
    },
    "content_sources": {
        "story_content": true,
        "storyboard_images": true,
        "character_voices": true,
        "background_music": true
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "视频生成任务创建成功",
    "data": {
        "task_id": "video_task_001",
        "project_id": "project_001",
        "platform": "KlingAI",
        "platform_model": "kling_video_v1",
        "video_settings": {
            "duration": 300,
            "quality": "high",
            "resolution": "1920x1080",
            "frame_rate": 30
        },
        "task_status": "processing",
        "created_at": "2024-01-01 15:40:00",
        "estimated_completion_time": "2024-01-01 16:10:00",
        "credits_cost": 500,
        "processing_queue": {
            "position": 1,
            "estimated_wait_time": 1800
        },
        "status_check_url": "/api/videos/video_task_001/status"
    },
    "timestamp": 1640999280,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 47.2 获取视频生成状态 GET /api/videos/{task_id}/status

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "video_task_001",
        "task_status": "completed",
        "progress": 100,
        "created_at": "2024-01-01 15:40:00",
        "completed_at": "2024-01-01 16:08:45",
        "processing_time": 1725,
        "result": {
            "video_url": "https://example.com/videos/video_task_001.mp4",
            "duration": 300.5,
            "file_size": "125MB",
            "resolution": "1920x1080",
            "frame_rate": 30,
            "quality_score": 0.96
        },
        "credits_used": 500
    },
    "timestamp": 1640999340,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 47.4 视频生成结果获取 GET /api/videos/{id}/result

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "video_task_001",
        "project_id": "project_001",
        "result": {
            "video_url": "https://example.com/videos/video_task_001.mp4",
            "download_url": "https://example.com/download/video_task_001.mp4",
            "duration": 300.5,
            "file_size": "125MB",
            "resolution": "1920x1080",
            "frame_rate": 30,
            "format": "mp4",
            "quality_score": 0.96,
            "thumbnail": "https://example.com/thumbnails/video_task_001.jpg"
        },
        "generation_metadata": {
            "platform": "KlingAI",
            "generation_time": 1725,
            "model_version": "v1.5.2"
        },
        "usage_info": {
            "credits_used": 500,
            "download_expires_at": "2024-01-08 16:08:45",
            "usage_rights": "personal_use"
        }
    },
    "timestamp": 1640999400,
    "request_id": "req_abc123_def456"
}
```

---

## 🎪 **第六阶段：作品广场** (8个接口)

### 📋 **测试目标**
验证作品发布和展示功能，测试可选发布机制。

### 🎨 **6.1 作品展示和发布**

#### 步骤1: 49.5 作品展示库 GET /api/works/gallery

**请求参数示例：**
```json
{
    "category": "all",
    "sort_by": "popularity",
    "page": 1,
    "per_page": 20,
    "filter": {
        "genre": "romance",
        "style": "modern"
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "works": [
            {
                "work_id": "work_001",
                "title": "繁华都市中的温暖邂逅",
                "author": "用户12345",
                "category": "story_video",
                "genre": "romance",
                "style": "modern",
                "thumbnail": "https://example.com/thumbnails/work_001.jpg",
                "preview_video": "https://example.com/previews/work_001.mp4",
                "duration": 300,
                "view_count": 1250,
                "like_count": 89,
                "rating": 4.8,
                "created_at": "2024-01-01 16:15:00",
                "is_featured": true
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 156,
            "last_page": 8
        },
        "categories": [
            {"name": "story_video", "count": 89},
            {"name": "music_video", "count": 45},
            {"name": "art_gallery", "count": 22}
        ]
    },
    "timestamp": 1640999460,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999460,
    "request_id": "req_abc123_def456"
}
```

#### 步骤2: 41.7 作品广场 GET /api/publications/plaza

**请求参数示例：**
```json
{
    "category": "featured",
    "sort_by": "trending",
    "page": 1,
    "per_page": 20,
    "time_range": "week"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "featured_works": [
            {
                "publication_id": "pub_001",
                "work_id": "work_001",
                "title": "繁华都市中的温暖邂逅",
                "author": {
                    "user_id": "user_12345",
                    "username": "创作者小王",
                    "avatar": "https://example.com/avatars/user_12345.jpg"
                },
                "category": "story_video",
                "genre": "romance",
                "style": "modern",
                "thumbnail": "https://example.com/thumbnails/pub_001.jpg",
                "preview_video": "https://example.com/previews/pub_001.mp4",
                "duration": 300,
                "description": "一个关于都市白领寻找真爱的温馨故事",
                "tags": ["都市", "爱情", "温馨", "现代"],
                "statistics": {
                    "view_count": 2580,
                    "like_count": 156,
                    "comment_count": 23,
                    "share_count": 45,
                    "rating": 4.8,
                    "rating_count": 89
                },
                "published_at": "2024-01-01 16:15:00",
                "is_featured": true,
                "is_trending": true,
                "trending_score": 95.6
            },
            {
                "publication_id": "pub_002",
                "work_id": "work_002",
                "title": "星际探险记",
                "author": {
                    "user_id": "user_67890",
                    "username": "科幻迷",
                    "avatar": "https://example.com/avatars/user_67890.jpg"
                },
                "category": "story_video",
                "genre": "sci-fi",
                "style": "futuristic",
                "thumbnail": "https://example.com/thumbnails/pub_002.jpg",
                "preview_video": "https://example.com/previews/pub_002.mp4",
                "duration": 450,
                "description": "2050年人类探索银河系的冒险故事",
                "tags": ["科幻", "探险", "未来", "太空"],
                "statistics": {
                    "view_count": 1890,
                    "like_count": 134,
                    "comment_count": 18,
                    "share_count": 32,
                    "rating": 4.6,
                    "rating_count": 67
                },
                "published_at": "2024-01-01 14:30:00",
                "is_featured": true,
                "is_trending": true,
                "trending_score": 88.2
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 245,
            "last_page": 13
        },
        "plaza_statistics": {
            "total_publications": 245,
            "total_views_today": 15680,
            "trending_works": 12,
            "featured_works": 8,
            "new_publications_today": 5
        },
        "categories": [
            {"name": "story_video", "count": 156, "trending": true},
            {"name": "music_video", "count": 67, "trending": false},
            {"name": "art_gallery", "count": 22, "trending": false}
        ]
    },
    "timestamp": 1640999520,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "category": ["分类不存在"],
            "sort_by": ["排序方式无效"],
            "time_range": ["时间范围无效，支持：day, week, month, all"]
        }
    },
    "timestamp": 1640999520,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999520,
    "request_id": "req_abc123_def456"
}
```

#### 步骤3: 41.9 热门作品 GET /api/publications/trending

**请求参数示例：**
```json
{
    "time_range": "week",
    "category": "all",
    "limit": 10,
    "include_stats": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "trending_works": [
            {
                "publication_id": "pub_001",
                "work_id": "work_001",
                "title": "繁华都市中的温暖邂逅",
                "author": {
                    "user_id": "user_12345",
                    "username": "创作者小王",
                    "avatar": "https://example.com/avatars/user_12345.jpg",
                    "verified": true
                },
                "category": "story_video",
                "genre": "romance",
                "thumbnail": "https://example.com/thumbnails/pub_001.jpg",
                "duration": 300,
                "trending_metrics": {
                    "trending_score": 95.6,
                    "rank": 1,
                    "rank_change": 0,
                    "view_growth": 0.45,
                    "engagement_rate": 0.12,
                    "viral_coefficient": 1.8
                },
                "statistics": {
                    "view_count": 2580,
                    "like_count": 156,
                    "comment_count": 23,
                    "share_count": 45,
                    "rating": 4.8
                },
                "published_at": "2024-01-01 16:15:00",
                "trending_since": "2024-01-01 18:00:00"
            },
            {
                "publication_id": "pub_003",
                "work_id": "work_003",
                "title": "古风仙侠传说",
                "author": {
                    "user_id": "user_11111",
                    "username": "古风大师",
                    "avatar": "https://example.com/avatars/user_11111.jpg",
                    "verified": false
                },
                "category": "story_video",
                "genre": "fantasy",
                "thumbnail": "https://example.com/thumbnails/pub_003.jpg",
                "duration": 420,
                "trending_metrics": {
                    "trending_score": 89.3,
                    "rank": 2,
                    "rank_change": 1,
                    "view_growth": 0.38,
                    "engagement_rate": 0.15,
                    "viral_coefficient": 1.6
                },
                "statistics": {
                    "view_count": 1950,
                    "like_count": 189,
                    "comment_count": 34,
                    "share_count": 28,
                    "rating": 4.7
                },
                "published_at": "2024-01-01 12:45:00",
                "trending_since": "2024-01-01 15:30:00"
            }
        ],
        "trending_analysis": {
            "total_trending": 10,
            "time_range": "week",
            "analysis_date": "2024-01-01 20:00:00",
            "top_categories": [
                {"category": "story_video", "count": 7},
                {"category": "music_video", "count": 2},
                {"category": "art_gallery", "count": 1}
            ],
            "trending_factors": [
                {"factor": "view_growth", "weight": 0.4},
                {"factor": "engagement_rate", "weight": 0.3},
                {"factor": "viral_coefficient", "weight": 0.3}
            ]
        },
        "update_frequency": "每小时更新",
        "next_update": "2024-01-01 21:00:00"
    },
    "timestamp": 1640999580,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "time_range": ["时间范围无效，支持：day, week, month"],
            "limit": ["限制数量必须在1-50之间"]
        }
    },
    "timestamp": 1640999580,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999580,
    "request_id": "req_abc123_def456"
}
```

#### 步骤4: 41.8 获取作品详情 GET /api/publications/{id}/detail

**请求参数示例：**
```json
{
    "id": "pub_001",
    "include_comments": true,
    "include_related": true,
    "track_view": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "publication_id": "pub_001",
        "work_id": "work_001",
        "title": "繁华都市中的温暖邂逅",
        "subtitle": "一个关于爱情与成长的都市故事",
        "author": {
            "user_id": "user_12345",
            "username": "创作者小王",
            "avatar": "https://example.com/avatars/user_12345.jpg",
            "bio": "专注都市情感故事创作",
            "verified": true,
            "follower_count": 1250,
            "work_count": 8
        },
        "content": {
            "category": "story_video",
            "genre": "romance",
            "style": "modern",
            "video_url": "https://example.com/videos/pub_001.mp4",
            "thumbnail": "https://example.com/thumbnails/pub_001.jpg",
            "duration": 300,
            "resolution": "1920x1080",
            "file_size": "125MB"
        },
        "description": "讲述了都市白领林雅婷在繁忙工作中偶遇咖啡店老板陈浩然，两人从陌生到相知相爱的温馨故事。通过细腻的情感描写和真实的都市生活场景，展现了现代人在快节奏生活中对真挚情感的渴望。",
        "tags": ["都市", "爱情", "温馨", "现代", "白领", "咖啡店"],
        "statistics": {
            "view_count": 2580,
            "unique_viewers": 2234,
            "like_count": 156,
            "dislike_count": 3,
            "comment_count": 23,
            "share_count": 45,
            "favorite_count": 89,
            "rating": 4.8,
            "rating_count": 89,
            "completion_rate": 0.85
        },
        "engagement": {
            "average_watch_time": 255,
            "replay_rate": 0.12,
            "share_rate": 0.017,
            "like_rate": 0.060,
            "comment_rate": 0.009
        },
        "published_at": "2024-01-01 16:15:00",
        "updated_at": "2024-01-01 16:15:00",
        "is_featured": true,
        "is_trending": true,
        "trending_rank": 1,
        "comments": [
            {
                "comment_id": "comment_001",
                "user": {
                    "user_id": "user_99999",
                    "username": "观众A",
                    "avatar": "https://example.com/avatars/user_99999.jpg"
                },
                "content": "太感人了！都市生活中的温暖瞬间被描绘得很真实。",
                "like_count": 12,
                "created_at": "2024-01-01 17:30:00",
                "is_author_reply": false
            },
            {
                "comment_id": "comment_002",
                "user": {
                    "user_id": "user_12345",
                    "username": "创作者小王",
                    "avatar": "https://example.com/avatars/user_12345.jpg"
                },
                "content": "谢谢大家的支持！希望这个故事能给大家带来温暖。",
                "like_count": 8,
                "created_at": "2024-01-01 18:00:00",
                "is_author_reply": true
            }
        ],
        "related_works": [
            {
                "publication_id": "pub_004",
                "title": "都市夜未央",
                "author": "创作者小李",
                "thumbnail": "https://example.com/thumbnails/pub_004.jpg",
                "similarity_score": 0.85
            }
        ],
        "user_interaction": {
            "has_liked": false,
            "has_favorited": false,
            "has_commented": false,
            "watch_progress": 0,
            "last_watched": null
        }
    },
    "timestamp": 1640999640,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (404 - 作品不存在)：**
```json
{
    "code": 404,
    "message": "作品不存在",
    "data": {
        "publication_id": "invalid_pub_id",
        "suggestion": "请检查作品ID是否正确"
    },
    "timestamp": 1640999640,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999640,
    "request_id": "req_abc123_def456"
}
```

#### 步骤5: 49.1 发布作品 POST /api/works/publish

**请求参数示例：**
```json
{
    "work_id": "work_001",
    "publication_settings": {
        "title": "繁华都市中的温暖邂逅",
        "description": "讲述了都市白领林雅婷在繁忙工作中偶遇咖啡店老板陈浩然，两人从陌生到相知相爱的温馨故事。",
        "tags": ["都市", "爱情", "温馨", "现代"],
        "category": "story_video",
        "visibility": "public",
        "allow_comments": true,
        "allow_downloads": false
    },
    "content_settings": {
        "thumbnail_url": "https://example.com/thumbnails/work_001.jpg",
        "preview_start_time": 10,
        "preview_duration": 30
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "作品发布成功",
    "data": {
        "publication_id": "pub_new_001",
        "work_id": "work_001",
        "title": "繁华都市中的温暖邂逅",
        "publication_url": "https://example.com/works/pub_new_001",
        "share_url": "https://example.com/share/pub_new_001",
        "publication_settings": {
            "title": "繁华都市中的温暖邂逅",
            "description": "讲述了都市白领林雅婷在繁忙工作中偶遇咖啡店老板陈浩然，两人从陌生到相知相爱的温馨故事。",
            "tags": ["都市", "爱情", "温馨", "现代"],
            "category": "story_video",
            "visibility": "public",
            "allow_comments": true,
            "allow_downloads": false
        },
        "content_info": {
            "video_url": "https://example.com/videos/pub_new_001.mp4",
            "thumbnail_url": "https://example.com/thumbnails/work_001.jpg",
            "duration": 300,
            "file_size": "125MB",
            "resolution": "1920x1080"
        },
        "publication_status": "published",
        "published_at": "2024-01-01 16:45:00",
        "moderation_status": "approved",
        "initial_statistics": {
            "view_count": 0,
            "like_count": 0,
            "comment_count": 0,
            "share_count": 0
        },
        "estimated_reach": {
            "potential_viewers": 5000,
            "target_audience_match": 0.78,
            "trending_probability": 0.65
        },
        "promotion_suggestions": [
            "添加更多相关标签提高发现率",
            "在社交媒体分享增加曝光",
            "与其他创作者互动提升活跃度"
        ]
    },
    "timestamp": 1640999700,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "work_id": ["作品ID不存在"],
            "publication_settings.title": ["标题不能为空", "标题长度不能超过100字符"],
            "publication_settings.description": ["描述长度不能超过1000字符"],
            "publication_settings.tags": ["标签不能为空", "标签数量不能超过10个"]
        }
    },
    "timestamp": 1640999700,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (1018 - 作品已发布)：**
```json
{
    "code": 1018,
    "message": "作品已发布",
    "data": {
        "work_id": "work_001",
        "existing_publication_id": "pub_001",
        "published_at": "2024-01-01 16:15:00",
        "suggestion": "如需更新发布信息，请使用更新接口"
    },
    "timestamp": 1640999700,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999700,
    "request_id": "req_abc123_def456"
}
```

#### 步骤6: 41.1 发布作品 POST /api/publications/publish

**请求参数示例：**
```json
{
    "work_id": "work_002",
    "publication_data": {
        "title": "星际探险记",
        "description": "2050年人类探索银河系的冒险故事，充满未知的挑战和发现。",
        "tags": ["科幻", "探险", "未来", "太空"],
        "category": "story_video",
        "genre": "sci-fi",
        "target_audience": "young_adult",
        "content_rating": "general",
        "language": "zh-CN"
    },
    "publication_options": {
        "visibility": "public",
        "allow_comments": true,
        "allow_ratings": true,
        "allow_downloads": false,
        "enable_monetization": false,
        "schedule_publish": false
    },
    "metadata": {
        "thumbnail_url": "https://example.com/thumbnails/work_002.jpg",
        "preview_settings": {
            "start_time": 15,
            "duration": 45
        },
        "seo_keywords": ["科幻故事", "太空探险", "未来世界"]
    }
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "作品发布成功",
    "data": {
        "publication_id": "pub_new_002",
        "work_id": "work_002",
        "title": "星际探险记",
        "publication_url": "https://example.com/publications/pub_new_002",
        "share_url": "https://example.com/share/pub_new_002",
        "embed_code": "<iframe src='https://example.com/embed/pub_new_002' width='800' height='450'></iframe>",
        "publication_data": {
            "title": "星际探险记",
            "description": "2050年人类探索银河系的冒险故事，充满未知的挑战和发现。",
            "tags": ["科幻", "探险", "未来", "太空"],
            "category": "story_video",
            "genre": "sci-fi",
            "target_audience": "young_adult",
            "content_rating": "general",
            "language": "zh-CN"
        },
        "content_info": {
            "video_url": "https://example.com/videos/pub_new_002.mp4",
            "thumbnail_url": "https://example.com/thumbnails/work_002.jpg",
            "duration": 450,
            "file_size": "180MB",
            "resolution": "1920x1080",
            "format": "mp4"
        },
        "publication_status": "published",
        "moderation_status": "pending_review",
        "published_at": "2024-01-01 17:00:00",
        "visibility": "public",
        "statistics": {
            "view_count": 0,
            "like_count": 0,
            "comment_count": 0,
            "share_count": 0,
            "rating": 0,
            "rating_count": 0
        },
        "discovery_info": {
            "search_keywords": ["科幻故事", "太空探险", "未来世界", "星际", "探险"],
            "recommended_categories": ["科幻", "冒险", "未来"],
            "target_audience_tags": ["科幻爱好者", "年轻观众", "探险迷"],
            "estimated_reach": 3500
        },
        "author_info": {
            "user_id": "user_67890",
            "username": "科幻迷",
            "total_publications": 3,
            "follower_count": 890
        }
    },
    "timestamp": 1640999760,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "work_id": ["作品ID不存在或无权限"],
            "publication_data.title": ["标题不能为空"],
            "publication_data.category": ["分类不存在"],
            "publication_data.tags": ["至少需要一个标签"]
        }
    },
    "timestamp": 1640999760,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999760,
    "request_id": "req_abc123_def456"
}
```

#### 步骤7: 49.4 获取我的作品 GET /api/works/my-works

**请求参数示例：**
```json
{
    "page": 1,
    "per_page": 20,
    "status": "all",
    "sort_by": "created_at",
    "sort_order": "desc",
    "include_unpublished": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "works": [
            {
                "work_id": "work_001",
                "title": "繁华都市中的温暖邂逅",
                "category": "story_video",
                "genre": "romance",
                "style": "modern",
                "status": "completed",
                "publication_status": "published",
                "publication_id": "pub_001",
                "thumbnail": "https://example.com/thumbnails/work_001.jpg",
                "duration": 300,
                "file_size": "125MB",
                "created_at": "2024-01-01 15:00:00",
                "updated_at": "2024-01-01 16:45:00",
                "completed_at": "2024-01-01 16:08:45",
                "published_at": "2024-01-01 16:45:00",
                "statistics": {
                    "view_count": 2580,
                    "like_count": 156,
                    "comment_count": 23,
                    "share_count": 45,
                    "rating": 4.8
                },
                "project_info": {
                    "project_id": "project_001",
                    "story_chapters": 5,
                    "total_words": 4000,
                    "characters_count": 2,
                    "images_count": 10
                },
                "privacy": "public",
                "monetization": {
                    "enabled": false,
                    "earnings": 0
                }
            },
            {
                "work_id": "work_003",
                "title": "未完成的科幻故事",
                "category": "story_video",
                "genre": "sci-fi",
                "style": "futuristic",
                "status": "in_progress",
                "publication_status": "unpublished",
                "publication_id": null,
                "thumbnail": "https://example.com/thumbnails/work_003.jpg",
                "duration": 0,
                "file_size": "0MB",
                "created_at": "2024-01-01 18:00:00",
                "updated_at": "2024-01-01 19:30:00",
                "completed_at": null,
                "published_at": null,
                "progress": {
                    "story_generation": "completed",
                    "character_binding": "in_progress",
                    "image_generation": "pending",
                    "video_generation": "pending",
                    "overall_progress": 35
                },
                "project_info": {
                    "project_id": "project_003",
                    "story_chapters": 8,
                    "total_words": 6400,
                    "characters_count": 1,
                    "images_count": 0
                },
                "privacy": "private",
                "estimated_completion": "2024-01-02 12:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 2,
            "last_page": 1
        },
        "statistics": {
            "total_works": 2,
            "completed_works": 1,
            "in_progress_works": 1,
            "published_works": 1,
            "unpublished_works": 1,
            "total_views": 2580,
            "total_likes": 156,
            "total_duration": 300
        },
        "storage_info": {
            "used_storage": "125MB",
            "total_storage": "10GB",
            "usage_percentage": 1.25
        }
    },
    "timestamp": 1640999820,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "status": ["状态值无效，支持：all, completed, in_progress, failed"],
            "sort_by": ["排序字段无效"],
            "per_page": ["每页数量必须在1-50之间"]
        }
    },
    "timestamp": 1640999820,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999820,
    "request_id": "req_abc123_def456"
}
```

#### 步骤8: 41.6 我的发布列表 GET /api/publications/my-publications

**请求参数示例：**
```json
{
    "page": 1,
    "per_page": 20,
    "status": "published",
    "sort_by": "published_at",
    "sort_order": "desc",
    "include_stats": true
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "publications": [
            {
                "publication_id": "pub_001",
                "work_id": "work_001",
                "title": "繁华都市中的温暖邂逅",
                "category": "story_video",
                "genre": "romance",
                "thumbnail": "https://example.com/thumbnails/pub_001.jpg",
                "duration": 300,
                "publication_status": "published",
                "moderation_status": "approved",
                "visibility": "public",
                "published_at": "2024-01-01 16:45:00",
                "updated_at": "2024-01-01 16:45:00",
                "statistics": {
                    "view_count": 2580,
                    "unique_viewers": 2234,
                    "like_count": 156,
                    "dislike_count": 3,
                    "comment_count": 23,
                    "share_count": 45,
                    "favorite_count": 89,
                    "rating": 4.8,
                    "rating_count": 89,
                    "completion_rate": 0.85
                },
                "performance": {
                    "trending_rank": 1,
                    "is_featured": true,
                    "is_trending": true,
                    "trending_score": 95.6,
                    "engagement_rate": 0.12,
                    "viral_coefficient": 1.8
                },
                "monetization": {
                    "enabled": false,
                    "total_earnings": 0,
                    "this_month_earnings": 0
                },
                "publication_url": "https://example.com/publications/pub_001",
                "analytics_url": "https://example.com/analytics/pub_001"
            },
            {
                "publication_id": "pub_new_002",
                "work_id": "work_002",
                "title": "星际探险记",
                "category": "story_video",
                "genre": "sci-fi",
                "thumbnail": "https://example.com/thumbnails/pub_new_002.jpg",
                "duration": 450,
                "publication_status": "published",
                "moderation_status": "pending_review",
                "visibility": "public",
                "published_at": "2024-01-01 17:00:00",
                "updated_at": "2024-01-01 17:00:00",
                "statistics": {
                    "view_count": 45,
                    "unique_viewers": 42,
                    "like_count": 3,
                    "dislike_count": 0,
                    "comment_count": 1,
                    "share_count": 2,
                    "favorite_count": 1,
                    "rating": 5.0,
                    "rating_count": 1,
                    "completion_rate": 0.92
                },
                "performance": {
                    "trending_rank": null,
                    "is_featured": false,
                    "is_trending": false,
                    "trending_score": 12.3,
                    "engagement_rate": 0.07,
                    "viral_coefficient": 0.04
                },
                "monetization": {
                    "enabled": false,
                    "total_earnings": 0,
                    "this_month_earnings": 0
                },
                "publication_url": "https://example.com/publications/pub_new_002",
                "analytics_url": "https://example.com/analytics/pub_new_002"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 2,
            "last_page": 1
        },
        "summary_statistics": {
            "total_publications": 2,
            "published_count": 2,
            "pending_review_count": 1,
            "approved_count": 1,
            "total_views": 2625,
            "total_likes": 159,
            "total_comments": 24,
            "total_shares": 47,
            "average_rating": 4.85,
            "total_earnings": 0
        },
        "performance_insights": {
            "best_performing": "pub_001",
            "most_engaging": "pub_001",
            "trending_publications": 1,
            "featured_publications": 1,
            "growth_trend": "positive",
            "engagement_trend": "stable"
        }
    },
    "timestamp": 1640999880,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "status": ["状态值无效，支持：published, pending_review, rejected"],
            "sort_by": ["排序字段无效"],
            "per_page": ["每页数量必须在1-50之间"]
        }
    },
    "timestamp": 1640999880,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640999880,
    "request_id": "req_abc123_def456"
}
```
